import logger from '../../logger.service';

/**
 * 資料驗證輔助類別
 * 提供統一的資料驗證功能，避免在各個 Use Case 中重複實作
 *
 * 注意：這是一個純工具類別，不包含業務邏輯，符合 Clean Architecture 原則
 */
export class DataValidatorHelper {
    private readonly company: string;

    constructor(company: string) {
        this.company = company;
    }

    /**
     * 驗證必要欄位
     * @param data - 要驗證的資料
     * @param requiredFields - 必要欄位列表
     * @param entityName - 實體名稱（用於錯誤訊息）
     * @returns 驗證結果
     */
    validateRequiredFields(
        data: any,
        requiredFields: string[],
        entityName = 'entity',
    ): { isValid: boolean; missingFields: string[] } {
        const missingFields: string[] = [];

        for (const field of requiredFields) {
            if (!data[field]) {
                missingFields.push(field);
            }
        }

        const isValid = missingFields.length === 0;

        if (!isValid) {
            logger.error(
                `[${
                    this.company
                }] Missing required fields for ${entityName}: ${missingFields.join(
                    ', ',
                )}`,
            );
        }

        return { isValid, missingFields };
    }

    /**
     * 驗證訂單資料
     * @param authorizeRecord - 授權記錄
     * @returns 是否有效
     */
    validateOrderData(authorizeRecord: any): boolean {
        if (!authorizeRecord.orderId) {
            logger.error(
                `[${this.company}] Order ID not found for RS ID: ${authorizeRecord.rsId}`,
            );
            return false;
        }

        return true;
    }

    /**
     * 驗證出貨參數
     * @param authorizeRecord - 授權記錄
     * @returns 是否有效
     */
    validateShipmentData(authorizeRecord: any): boolean {
        const requiredFields = ['storeId', 'orderId', 'rsId'];
        const { isValid } = this.validateRequiredFields(
            authorizeRecord,
            requiredFields,
            'shipment',
        );
        return isValid;
    }

    /**
     * 驗證付款回應代碼
     * @param respondCode - 回應代碼
     * @returns 是否為成功
     */
    isPaymentSuccessful(respondCode: string): boolean {
        return respondCode === '00';
    }

    /**
     * 驗證陣列是否為空
     * @param array - 要檢查的陣列
     * @param arrayName - 陣列名稱（用於日誌）
     * @returns 是否為空
     */
    isEmptyArray<T>(array: T[], arrayName: string): boolean {
        if (array.length === 0) {
            logger.info(`[${this.company}] No ${arrayName} to process`);
            return true;
        }
        return false;
    }

    /**
     * 驗證日期格式
     * @param date - 日期字串或物件
     * @param fieldName - 欄位名稱
     * @returns 是否有效
     */
    validateDate(date: any, fieldName: string): boolean {
        if (!date) {
            logger.error(`[${this.company}] ${fieldName} is required`);
            return false;
        }

        const dateObj = new Date(date);
        if (isNaN(dateObj.getTime())) {
            logger.error(`[${this.company}] Invalid ${fieldName}: ${date}`);
            return false;
        }

        return true;
    }

    /**
     * 驗證字串長度
     * @param value - 要驗證的字串
     * @param fieldName - 欄位名稱
     * @param minLength - 最小長度
     * @param maxLength - 最大長度
     * @returns 是否有效
     */
    validateStringLength(
        value: string,
        fieldName: string,
        minLength = 0,
        maxLength: number = Number.MAX_SAFE_INTEGER,
    ): boolean {
        if (!value) {
            logger.error(`[${this.company}] ${fieldName} is required`);
            return false;
        }

        if (value.length < minLength) {
            logger.error(
                `[${this.company}] ${fieldName} must be at least ${minLength} characters`,
            );
            return false;
        }

        if (value.length > maxLength) {
            logger.error(
                `[${this.company}] ${fieldName} must not exceed ${maxLength} characters`,
            );
            return false;
        }

        return true;
    }

    /**
     * 驗證數字範圍
     * @param value - 要驗證的數字
     * @param fieldName - 欄位名稱
     * @param min - 最小值
     * @param max - 最大值
     * @returns 是否有效
     */
    validateNumberRange(
        value: number,
        fieldName: string,
        min: number = Number.MIN_SAFE_INTEGER,
        max: number = Number.MAX_SAFE_INTEGER,
    ): boolean {
        if (typeof value !== 'number' || isNaN(value)) {
            logger.error(
                `[${this.company}] ${fieldName} must be a valid number`,
            );
            return false;
        }

        if (value < min) {
            logger.error(
                `[${this.company}] ${fieldName} must be at least ${min}`,
            );
            return false;
        }

        if (value > max) {
            logger.error(
                `[${this.company}] ${fieldName} must not exceed ${max}`,
            );
            return false;
        }

        return true;
    }

    /**
     * 驗證電子郵件格式
     * @param email - 電子郵件
     * @param fieldName - 欄位名稱
     * @returns 是否有效
     */
    validateEmail(email: string, fieldName = 'email'): boolean {
        if (!email) {
            logger.error(`[${this.company}] ${fieldName} is required`);
            return false;
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            logger.error(
                `[${this.company}] Invalid ${fieldName} format: ${email}`,
            );
            return false;
        }

        return true;
    }
}
