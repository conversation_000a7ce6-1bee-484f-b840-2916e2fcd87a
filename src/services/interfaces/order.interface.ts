export interface Order {
    method: string; //付款方式 1:現金 2:信用卡
    period: number; // 期數
    cardNumber: string; // 卡號
    validThru: string; // 有效日期 YYMM、以卡片上有效日期為準則
    personId?: string; // 身分證字號
    amount: number; //交易金額
    orderId: string; //訂單單號
    applicationId: string; //申請書編號
    shipperId: string; //出貨單號
    startDate: Date; // 起始日
    endDate: Date; // 到期日
    isPaid: boolean; // 繳費狀態
    company: string; // 公司編號
    storeId: string; // 門市編號
    itemCode: string; // 料號
    orderDate: string; // 出貨日期
    active: string; //是否有效
    SN: string; //序號
    line: number; //品項流水序號
    isACH: boolean;
    bankCode: string;
    bankBranchCode: string;
    bankAccount: string;
    bankIdentityCard: string;
    memberCode: string;
}

export interface SearchOrderResult extends Order {
    items: {
        line: number; //品項流水序號
        materialCode: string; // 料號
    }[];
}

export enum ShipperType {
    RS = 'RS',
    HA = 'HA',
    RSM = 'RSM',
    FOOD = 'FOOD',
}

export interface OrderType {
    /** RS訂閱訂單單別 */
    rsOrder: string;
    /** RS訂閱申請單單別 */
    rsApplication: string;
    /** 訂閱出貨單單別(RS) */
    rsShipper: string;
    /** HA訂閱訂單單別 */
    haOrder: string;
    /** HA訂閱申請單單別 */
    haApplication: string;
    /** 訂閱出貨單單別(HA) */
    haShipper: string;
    /** RS面罩訂閱訂單 */
    rsMaskOrder: string;
    /** RS面罩訂閱申請書 */
    rsMaskApplication: string;
    /** RS面罩訂閱出貨單 */
    rsMaskShipper: string;
    /** 科立健食品訂閱訂單 */
    ccFoodOrder: string;
    /** 科立健食品訂閱申請書 */
    ccFoodApplication: string;
    /** 科立健食品訂閱制出貨單 */
    ccFoodShipper: string;
}

export interface OrderTypeResult {
    type: ShipperType;
    /** 訂閱訂單單別 */
    order: string;
    /** 訂閱申請單單別 */
    application: string;
    /** 訂閱出貨單單別 */
    shipPrefixCode: string;
}

export interface IUpdateShipperResult {
    total: number;
    cc: number;
    cash: number;
    ACH: number;
    free: number;
}

export interface WPCFee {
    RSID: string;
    PERIOD: number;
    FEEDATE: Date;
    FEE: number;
    FEESTATE: string;
    FEEMETHOD: string;
    SHIPID: string;
    ACTIVE: string;
    CREATEDATE: Date;
    CREATETIME: string;
    STOREID: string;
    LEGAL: string;
    TOTALPERIOD: number;
    EXPECTEDFEE: number;
}

export interface CreateNextPeriodParams {
    applicationId: string;
    period: number;
}

export interface ISearchFee {
    orderId?: string;
    applicationId?: string;
    date?: string;
    shipperId?: string;
    isCard?: boolean;
    isACH?: boolean;
    period?: number;
    feeState?: string;
    betweenDate?: {
        date1?: string;
        date2?: string;
    };
    skipFeeState?: boolean;
}

export interface ISearchVSubApp {
    rsId?: string;
    orderId?: string;
    nextShippingDate?: string;
    estimatedShippingDate?: string;
    estimatedCardDate?: string;
    applicationId?: string;
    date?: string;
    shipperId?: string;
    isCard?: string;
    isACH?: boolean;
    period?: number;
    feeState?: string;
    betweenDate?: {
        date1?: string;
        date2?: string;
    };
    skipFeeState?: boolean;
    isRepay?: boolean;
}

export interface IFoodOrderForm {
    company: string; // 公司別
    orderDate: string; // 訂單日
    nextShippingDate: string; // 下次出貨日
    regOrderDay: number; // 定期訂購日
    rsId: string; //申請書編號
    orderSeqId: number; // 訂單項次
    period: number; // 期數
    cardNumber: string; // 卡號
    validThru: string; // 有效日期 YYMM、以卡片上有效日期為準則
    memberCode: string;
    memberName: string;
    storeId: string; // 門市編號
    storeName: string; // 門市名稱
    userId: string; // 聽力師編號
    amount: number; //交易金額
    legal: string; // 歸屬法人

    // personId?: string; // 身分證字號
    orderId?: string; //訂單單號
    // shipperId: string; //出貨單號
    // startDate: Date; // 起始日
    // endDate: Date; // 到期日
    // isPaid: boolean; // 繳費狀態
    // company: string; // 公司編號
    // itemCode: string; // 料號
    //
    // active: string; //是否有效
    // SN: string; //序號
    // line: number; //品項流水序號
    // isACH: boolean;
    // bankCode: string;
    // bankBranchCode: string;
    // bankAccount: string;
    // bankIdentityCard: string;
    isRepay?: boolean;
}

export interface ISearchSubscribeDetail {
    rsId?: string;
    period?: number;
    estimatedCardDate?: Date;
    estimatedShippingDate?: Date;
    orderId?: string;
    shippingId?: string;
    isProcessed?: boolean;
    isCompleted?: boolean;
    isReCard?: boolean;
    storeId?: string;
    legal?: string;
}

export interface IWPCShipmentParams {
    storeId: string;
    rsId: string;
    orderId: string;
    date: Date;
}
