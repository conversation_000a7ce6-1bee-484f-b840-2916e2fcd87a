/**
 * 共用工具和基礎類別統一匯出
 *
 * 這些工具類別遵循 Clean Architecture 原則：
 * - 不包含業務邏輯
 * - 可以被任何 Use Case 使用
 * - 不依賴於特定的 Use Case
 * - 提供純粹的技術功能
 */

export { BaseUseCase } from './base.usecase';
export { FileManagerHelper } from './fileManager.helper';
export { EsunServiceHelper } from './esunService.helper';
export { DataValidatorHelper } from './dataValidator.helper';

// 匯入用於工廠類別
import { FileManagerHelper } from './fileManager.helper';
import { EsunServiceHelper } from './esunService.helper';
import { DataValidatorHelper } from './dataValidator.helper';

/**
 * 共用工具工廠類別
 * 提供統一的方式來創建和管理所有共用工具
 */
export class SharedHelpersFactory {
    private readonly company: string;
    private readonly helpers: Map<string, any> = new Map();

    constructor(company: string) {
        this.company = company;
        this.initializeHelpers();
    }

    /**
     * 初始化所有共用工具
     */
    private initializeHelpers(): void {
        this.helpers.set('fileManager', new FileManagerHelper(this.company));
        this.helpers.set('esunService', new EsunServiceHelper(this.company));
        this.helpers.set(
            'dataValidator',
            new DataValidatorHelper(this.company),
        );
    }

    /**
     * 取得檔案管理工具
     */
    getFileManagerHelper(): FileManagerHelper {
        return this.helpers.get('fileManager');
    }

    /**
     * 取得玉山服務工具
     */
    getEsunServiceHelper(): EsunServiceHelper {
        return this.helpers.get('esunService');
    }

    /**
     * 取得資料驗證工具
     */
    getDataValidatorHelper(): DataValidatorHelper {
        return this.helpers.get('dataValidator');
    }

    /**
     * 取得所有工具
     */
    getAllHelpers(): Map<string, any> {
        return new Map(this.helpers);
    }
}
