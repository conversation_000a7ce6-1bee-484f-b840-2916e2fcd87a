import { Utils, Helpers } from '@clinico/clinico-node-framework';
import * as moment from 'moment';
import configs from '../configs';

class SlackService {
    private clockImg: string;

    constructor() {
        Utils.Logger.Slack.initialize({
            url: configs.notification.slack.webhookUrl,
            defaults: undefined,
        });

        this.clockImg = 'https://img.icons8.com/B0B0B0/calendar';
    }

    async info(message: string): Promise<void> {
        await Utils.Logger.Slack.info({
            title: ':ghost: Info',
            text: message,
            footer: Helpers.Date.formatDateTime(moment()),
            footerIcon: this.clockImg,
        });
    }

    async warning(message: string): Promise<void> {
        await Utils.Logger.Slack.warning({
            title: ':face_with_monocle: WARNING!',
            text: message,
            footer: Helpers.Date.formatDateTime(moment()),
            footerIcon: this.clockImg,
        });
    }

    async error(message: string): Promise<void> {
        await Utils.Logger.Slack.error({
            title: ':poop: Oh shit!',
            text: message,
            footer: Helpers.Date.formatDateTime(moment()),
            footerIcon: this.clockImg,
        });
    }
}

const slack = new SlackService();
export default slack;
