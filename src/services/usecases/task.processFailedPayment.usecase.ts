import { FormService } from '../form.service';
import { OrderService } from '../order.service';
import { RepayService } from '../repay.service';
import { SMSService } from '../sms.service';
import { AskingService } from '../asking.service';
import { BaseUseCase, DataValidatorHelper } from './shared';
import { UpdatePaymentResult } from '../interfaces/payment.interface';
import { Order } from '../interfaces/order.interface';
import { IApplicationForm } from '../interfaces/form.interface';
import * as moment from 'moment';
import { insufficientAmountCodes } from '../../const';

/**
 * 處理失敗付款的業務用例
 * 負責處理付款失敗後的所有後續作業
 *
 * 重構後使用共用工具來避免重複程式碼
 */
export class ProcessFailedPaymentUseCase extends BaseUseCase {
    private formService: FormService;
    private orderService: OrderService;
    private repayService: RepayService;
    private smsService: SMSService;
    private askingService: AskingService;
    private dataValidatorHelper: DataValidatorHelper;

    constructor(company: string) {
        super(company);
        this.formService = new FormService(this.company);
        this.orderService = new OrderService(this.company);
        this.repayService = new RepayService(this.company);
        this.smsService = new SMSService(this.company);
        this.askingService = new AskingService(this.company);
        this.dataValidatorHelper = new DataValidatorHelper(company);
    }

    /**
     * 處理失敗付款的完整流程
     * @param shippingResult - 出貨單資料
     * @returns Asking ID
     */
    async execute(
        shippingResult: UpdatePaymentResult,
    ): Promise<number | undefined> {
        return this.executeWithErrorHandling(
            async () => {
                // 驗證出貨單資料
                this.validateShipperData(shippingResult);

                this.logInfo(
                    `Processing failed payment for ${shippingResult.applicationId}-${shippingResult.period}`,
                );

                // 1. 驗證並取得必要資料
                const { application, failedOrder } =
                    await this.validateAndGetData(shippingResult);

                // 2. 建立補刷紀錄
                await this.createRepayRecord({
                    failedOrder,
                    application,
                    errorCode: shippingResult.respondCode,
                });

                // 3. 發送失敗通知
                await this.sendFailureNotifications(shippingResult);

                // 4. 記錄 Asking 失敗案件
                const askingId = await this.recordAskingFailure(shippingResult);

                this.logWarn(
                    `Payment failed processing completed for ${shippingResult.applicationId}-${shippingResult.period} (#${askingId})`,
                );

                return askingId;
            },
            'process failed payment',
            {
                applicationId: shippingResult.applicationId,
                period: shippingResult.period,
            },
        );
    }

    /**
     * 驗證並取得失敗訂單的相關資料
     * @param shippingResult - 出貨單資料
     * @returns 申請書和失敗訂單資料
     */
    async validateAndGetData(shippingResult: UpdatePaymentResult): Promise<{
        application: IApplicationForm;
        failedOrder: Order;
    }> {
        const application = await this.formService.findOne(
            shippingResult.applicationId,
        );
        if (!application) {
            throw new Error(
                `Application not found: ${shippingResult.applicationId}`,
            );
        }

        const failedOrder = await this.orderService.findOneByApplicationPeriod(
            shippingResult.applicationId,
            shippingResult.period,
        );
        if (!failedOrder) {
            throw new Error(
                `Order not found for application ${shippingResult.applicationId}, period ${shippingResult.period}`,
            );
        }

        return { application, failedOrder };
    }

    /**
     * 建立補刷紀錄
     * @param failedOrder - 失敗的訂單資料
     * @param application - 申請書資料
     * @param errorCode - 錯誤代碼
     */
    async createRepayRecord(params: {
        failedOrder: Order;
        application: IApplicationForm;
        errorCode: string;
    }): Promise<void> {
        const { failedOrder, application, errorCode } = params;

        await this.repayService.create({
            applicationCode: failedOrder.applicationId,
            shipCode: failedOrder.shipperId,
            period: failedOrder.period,
            fee: failedOrder.amount,
            method: failedOrder.method,
            storeCode: failedOrder.storeId,
            userCode: application?.userCode,
            legal: failedOrder.company,
        });

        // [測試中不自動設定再次扣款]
        // // 銀行存款不足/簽帳金融卡餘額不足者，系統設定3日後自動再次扣款
        // if (insufficientAmountCodes.includes(errorCode)) {
        //     await this.repayService.updateFeeDate({
        //         applicationCode: failedOrder.applicationId,
        //         period: failedOrder.period,
        //         feeDate: moment().add(3, 'days').toDate(),
        //         memo: '銀行存款不足/簽帳金融卡餘額不足者，系統設定3日後自動再次扣款',
        //     });
        // }

        this.logInfo(
            `Repay record created for ${failedOrder.applicationId}-${failedOrder.period}`,
        );
    }

    /**
     * 發送失敗通知（簡訊）
     * @param shippingResult - 出貨單資料
     */
    async sendFailureNotifications(
        shippingResult: UpdatePaymentResult,
    ): Promise<void> {
        await this.smsService.sendFailedToMember(
            {
                orderType: shippingResult.orderType,
                applicationId: shippingResult.applicationId,
                period: shippingResult.period,
                errorCode: shippingResult.respondCode,
                errorMessage: shippingResult.respondMessage,
                storeCode: shippingResult.storeId,
                amount: shippingResult.amount,
                method: shippingResult.method,
            },
            false,
        );

        this.logInfo(
            `Failure SMS sent for ${shippingResult.applicationId}-${shippingResult.period}`,
        );
    }

    /**
     * 記錄 Asking 系統的失敗案件
     * @param shippingResult - 出貨單資料
     * @returns Asking ID
     */
    async recordAskingFailure(
        shippingResult: UpdatePaymentResult,
    ): Promise<number | undefined> {
        const askingId = await this.askingService.payFailed({
            orderType: shippingResult.orderType,
            applicationId: shippingResult.applicationId,
            period: shippingResult.period,
            errorMessage: shippingResult.respondMessage,
            storeCode: shippingResult.storeId,
        });

        if (askingId) {
            this.logInfo(
                `Asking failure recorded for ${shippingResult.applicationId}-${shippingResult.period} (#${askingId})`,
            );
        } else {
            this.logWarn(
                `Failed to record asking failure for ${shippingResult.applicationId}-${shippingResult.period}`,
            );
        }

        return askingId;
    }

    /**
     * 驗證出貨單資料
     * @param shippingResult - 出貨單資料
     */
    private validateShipperData(shippingResult: UpdatePaymentResult): void {
        const requiredFields = [
            'applicationId',
            'period',
            'orderType',
            'storeId',
        ];
        this.dataValidatorHelper.validateRequiredFields(
            shippingResult,
            requiredFields,
            'shippingResult',
        );
    }
}
