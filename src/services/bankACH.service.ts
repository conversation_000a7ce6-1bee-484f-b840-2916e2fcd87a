import * as fs from 'fs';
import * as mkdirp from 'mkdirp';
import * as moment from 'moment';
import configs from '../configs';
import { CLINICO, IB, SKD } from '../const';
import { ACHPRecord } from '../models/esun.ACH.model';
import stringUtil from '../utils/string';
import { Order, ShipperType } from './interfaces/order.interface';
import { Utils } from '@clinico/clinico-node-framework'; // 匯入 Utils
import {
    ACHRespondData,
    PaymentParmas,
    UpdatePaymentResult,
} from './interfaces/payment.interface';
import logger from './logger.service';
import { OrderService } from './order.service';
import * as readline from 'readline';
import { RepayService } from './repay.service';
import { AskingService } from './asking.service';
import { WpcService } from './wpc.service';
import { HolidayService } from './holiday.service';
import bufferUtil from '../utils/buffer';
import { FormService } from './form.service';
import { SMSService } from './sms.service'; // 匯入 SMSService

export class BankACHService {
    private company: string;
    private orderService: OrderService;
    private repayService: RepayService;
    private askingService: AskingService;
    private wpcService: WpcService;
    private holidayService: HolidayService;
    private formService: FormService;
    private smsService: SMSService; // 宣告 smsService

    // 用於稽核測試，收集 ACH 簡訊內容
    public static collectedSmsMessages: {
        applicationId: string;
        smsContent: string;
    }[] = [];

    constructor(company: string) {
        this.company = company;
        this.orderService = new OrderService(this.company);
        this.repayService = new RepayService(this.company);
        this.askingService = new AskingService(this.company);
        this.wpcService = new WpcService(this.company);
        this.formService = new FormService(this.company);
        this.holidayService = new HolidayService();
        this.smsService = new SMSService(this.company); // 實例化 smsService
    }

    async generateRequest(params: {
        orders: Order[];
        achDate: Date;
        isRepay: boolean;
    }): Promise<string | null> {
        try {
            const authorizeContext = await this.generateACHRequestRecord(
                params.orders,
                params.achDate,
            );
            if (authorizeContext.data.length == 0) {
                return null;
            }
            return await this.generateRequestFile(
                authorizeContext.request,
                params.achDate,
                params.isRepay,
            );
        } catch (err) {
            console.error(err);
            throw err;
        }
    }

    /** ACH入扣帳資料提出檔 */
    private async generateACHRequestRecord(
        orders: PaymentParmas[],
        achDate: Date,
    ): Promise<{ request: string; data: ACHPRecord[] }> {
        const data: ACHPRecord[] = [];
        const { bankCode, bankAccount, identity } = this.getBankInfo();
        const date = moment(achDate).add(-1911, 'year').format('YYYYMMDD'); //民國YYYYMMDD(為銀行入扣帳日前一營業日)
        let count = 0;
        let amount = 0;
        //Header
        let request = `${'BOF'}${'ACHP01'}${date}${moment().format('HHmmss')}${
            configs.bank.code
        }${'9990250'}${'V10'}${stringUtil.paddingRightForString(
            '',
            210,
            ' ',
        )}\n`;

        //data
        for (const order of orders) {
            if (this.ACHOrderValidate(order) == false) {
                continue;
            }

            //銀行<->WPC {type}-{訂閱申請單號}-{期數}
            const applicationType = order.applicationId.split('-')[0];
            const applicationSN = order.applicationId.split('-')[1];
            const shipperType =
                await this.orderService.getOderTypeByApplicationType(
                    applicationType,
                );
            const remark = stringUtil.paddingRightForString(
                `${shipperType}-${applicationSN}-${order.period}`,
                20,
                ' ',
            );

            const userBankCode = this.genBankCode({
                bankCode: order.bankCode,
                bankBranchCode: order.bankBranchCode,
            });

            const record: ACHPRecord = {
                P_TYPE: 'N',
                P_TXTYPE: 'SD', //SD:代收案件
                P_TXID: '909', //租金(營利)
                P_SEQ: stringUtil.paddingLeftForInteger(count + 1, 8, '0'),
                P_PBANK: bankCode,
                P_PCLNO: stringUtil.paddingLeftForString(bankAccount, 16, '0'),
                P_RBANK: userBankCode,
                P_RCLNO: stringUtil.paddingLeftForString(
                    order.bankAccount,
                    16,
                    '0',
                ),
                P_AMT: stringUtil.paddingLeftForInteger(order.amount, 10, '0'),
                P_RCODE: stringUtil.paddingRightForString('', 2, ' '),
                P_SCHD: 'B',
                P_CID: stringUtil.paddingRightForString(identity, 10, ' '),
                P_PID: stringUtil.paddingRightForString(
                    order.bankIdentityCard,
                    10,
                    ' ',
                ),
                P_SID: stringUtil.paddingRightForString('', 6, ' '),
                P_PDATE: stringUtil.paddingRightForString('', 8, ' '),
                P_PSEQ: stringUtil.paddingRightForString('', 8, ' '),
                P_PSCHD: stringUtil.paddingRightForString('', 1, ' '),
                P_CNO: stringUtil.paddingRightForString(
                    order.memberCode,
                    20,
                    ' ',
                ),
                P_NOTE: stringUtil.paddingRightForString(remark, 40, ' '),
                P_MEMO: stringUtil.paddingRightForString('', 10, ' '),
                P_CFEE: stringUtil.paddingLeftForInteger(0, 5, '0'),
                P_NOTEB: stringUtil.paddingRightForString('', 20, ' '),
                FILLER: stringUtil.paddingRightForString('', 39, ' '),
            };

            data.push(record);
            const jsonStr = JSON.stringify(record);
            const json = JSON.parse(jsonStr);

            for (const key in json) {
                request += json[key];
            }

            amount += order.amount;
            count++;
            request += '\n';
        }

        //Trailer
        request += `${'EOF'}${'ACHP01'}${date}${
            configs.bank.code
        }${'9990250'}${stringUtil.paddingLeftForInteger(
            count,
            8,
            '0',
        )}${stringUtil.paddingLeftForInteger(
            amount,
            16,
            '0',
        )}${stringUtil.paddingRightForString(
            ' ',
            8,
            ' ',
        )}${stringUtil.paddingRightForString(' ', 187, ' ')}`;
        return { request, data };
    }

    private ACHOrderValidate(order: PaymentParmas): boolean {
        if (order.method != '3') {
            return false;
        }
        if (!order.isACH) {
            logger.warn(`${order.applicationId} ACH disabled`);
            return false;
        }
        if (!order.bankCode) {
            logger.warn(`${order.applicationId} ACH bankCode is empty`);
            return false;
        }
        if (!order.bankBranchCode) {
            logger.warn(`${order.applicationId} ACH bankBranchCode is empty`);
            return false;
        }
        if (!order.bankAccount) {
            logger.warn(`${order.applicationId} ACH bankAccount is empty`);
            return false;
        }
        if (!order.bankIdentityCard) {
            logger.warn(`${order.applicationId} ACH bankIdentityCard is empty`);
            return false;
        }
        return true;
    }

    /** 分析ACH交易結果 */
    async generateResponse(buffer: Buffer): Promise<UpdatePaymentResult[]> {
        logger.info(`[${this.company}] Start generate ACH request`);
        const data: ACHRespondData[] = [];
        const stream = bufferUtil.bufferToStream(buffer);
        const rl = readline.createInterface({
            input: stream,
        });

        for await (const line of rl) {
            if (['BOFACHP01', 'EOFACHP01'].includes(line.substring(0, 9))) {
                continue;
            } else if (!['N', 'R'].includes(line.substring(0, 1))) {
                logger.warn(
                    `ACH回覆檔案內容異常(P-TYPE): ${line.substring(0, 1)}`,
                );
                continue;
            }
            if (line.length != 250) {
                logger.warn(`ACH回覆檔案內容異常(Length): ${line}`);
                continue;
            }

            const achRespondDataRecord: ACHPRecord = {
                P_TYPE: line.substring(0, 1),
                P_TXTYPE: line.substring(1, 3), //SD:代收案件
                P_TXID: line.substring(3, 6), //租金(營利)
                P_SEQ: line.substring(6, 14),
                P_PBANK: line.substring(14, 21),
                P_PCLNO: line.substring(21, 37),
                P_RBANK: line.substring(37, 44),
                P_RCLNO: line.substring(44, 60),
                P_AMT: line.substring(60, 70),
                P_RCODE: line.substring(70, 72),
                P_SCHD: line.substring(72, 73),
                P_CID: line.substring(73, 83),
                P_PID: line.substring(83, 93),
                P_SID: line.substring(93, 99),
                P_PDATE: line.substring(99, 107),
                P_PSEQ: line.substring(107, 115),
                P_PSCHD: line.substring(115, 116),
                P_CNO: line.substring(116, 146),
                P_NOTE: line.substring(136, 176),
                P_MEMO: line.substring(176, 186),
                P_CFEE: line.substring(186, 191),
                P_NOTEB: line.substring(191, 211),
                FILLER: line.substring(211, 250),
            };

            const remark =
                achRespondDataRecord.P_NOTE?.trim().replace(/[ ]|[*]/g, '') ??
                '';
            const shipperType = remark.split('-')[0];
            const oderTypeHearder = await this.orderService.getOderTypeHearder(
                shipperType,
            );
            if (!oderTypeHearder) {
                logger.warn(`ACH備註異常:${remark}`);
                continue;
            }

            const applicationSN = remark.split('-')[1];
            const period = Number(remark.split('-')[2]);
            const applicationId = `${oderTypeHearder.application}-${applicationSN}`;

            let order = await this.getFeeDetail({
                applicationId,
                period,
            });

            if (!order) {
                // 舊的RS單別
                if (remark.split('-')[0] == ShipperType.RS) {
                    const hearder = this.company == SKD ? 'RSWL1' : 'RSIB1';
                    order = await this.getFeeDetail({
                        applicationId: `${hearder}-${applicationSN}`,
                        period,
                    });

                    if (!order) {
                        continue;
                    }
                } else {
                    continue;
                }
            }

            const respondCode = achRespondDataRecord.P_RCODE?.trim()
                ? achRespondDataRecord.P_RCODE.trim()
                : '00';
            const respondMessage = this.getRespondMessage(respondCode);
            data.push({
                orderType: oderTypeHearder.type,
                shipperId: order.shipperId,
                bankAccount: `${order.bankCode}-${order.bankAccount}`,
                applicationId: order.applicationId,
                period: order.period,
                respondCode,
                respondMessage: respondMessage,
                authorizationCode: '',
                storeId: order.storeId,
                amount: order.amount,
            });
        }

        const shippingResult = await this.updateShip(data);
        await BankACHService.sendCollectedSmsEmail(); // 在 generateResponse 結束時發送稽核郵件
        return shippingResult;
    }

    private async updateShip(
        respondData: ACHRespondData[],
    ): Promise<UpdatePaymentResult[]> {
        const shippingResults = await this.wpcService.updateACHShippers(
            respondData,
        );
        logger.info(
            `[${this.company}]ShippingResult Updated! (${shippingResults.length})`,
        );

        for (const shippingResult of shippingResults) {
            await this.processSingleShipmentResult(shippingResult);
        }

        return shippingResults;
    }

    private async processSingleShipmentResult(
        shippingResult: UpdatePaymentResult,
    ): Promise<void> {
        const {
            applicationId,
            period,
            respondCode,
            respondMessage,
            shipperId,
        } = shippingResult;

        const repay = await this.repayService.findOneOrNull({
            applicationId,
            period,
            isProcess: true,
        });

        if (repay) {
            if (respondCode == '00') {
                await this.askingService.repayACHCompleted({
                    applicationId,
                    period,
                });
            } else {
                await this.askingService.repayACHFailed({
                    applicationId,
                    period,
                    errorMessage: respondMessage,
                });
            }
        } else if (respondCode != '00') {
            const application = await this.formService.findOne(applicationId);
            if (!application) {
                throw new Error('application not found');
            }
            const failed = await this.orderService.findOneByApplicationPeriod(
                applicationId,
                period,
            );
            if (!failed) {
                throw new Error('period of application not found');
            }
            //產生待補刷紀錄
            await this.repayService.create({
                applicationCode: applicationId,
                shipCode: shipperId,
                period,
                fee: failed.amount,
                method: failed.method,
                storeCode: failed.storeId,
                userCode: application?.userCode,
                legal: failed.company,
            });
            //ASKING API
            const askingId = await this.askingService.payFailedForACH({
                orderType: shippingResult.orderType,
                applicationId,
                period,
                errorMessage: respondMessage,
                storeCode: shippingResult.storeId,
            });
            logger.warn(`${respondMessage}(#${askingId})`);

            // ACH 交易失敗簡訊通知
            await this.smsService.sendFailedToMember(
                {
                    orderType: shippingResult.orderType,
                    applicationId,
                    period,
                    errorCode: respondCode,
                    errorMessage: respondMessage,
                    storeCode: shippingResult.storeId,
                    amount: shippingResult.amount,
                    method: shippingResult.method,
                },
                false, // ACH 失敗不是補刷
            );
        }
    }

    // 發送收集到的 ACH 簡訊稽核郵件
    public static async sendCollectedSmsEmail(): Promise<void> {
        if (configs.sms?.enableSending) {
            logger.info('簡訊發送已開啟，無需發送 ACH 稽核郵件。');
            return;
        }

        if (BankACHService.collectedSmsMessages.length === 0) {
            logger.info('沒有收集到 ACH 簡訊內容，無需發送稽核郵件。');
            return;
        }

        const emailSubject = `[稽核測試] ACH 繳費失敗簡訊內容彙整通知 - ${new Date().toLocaleDateString(
            'zh-TW',
        )}`;
        let emailBody = `
            <p>Hi Terry</p>
            <p>以下是本次 ACH 處理中收集到的繳費失敗簡訊內容：</p>
            <table border="1" style="border-collapse: collapse; width: 100%;">
                <thead>
                    <tr>
                        <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">訂單編號</th>
                        <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">簡訊內容</th>
                    </tr>
                </thead>
                <tbody>
        `;

        BankACHService.collectedSmsMessages.forEach((msg) => {
            emailBody += `
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd;">${msg.applicationId}</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">${msg.smsContent}</td>
                </tr>
            `;
        });

        emailBody += `
                </tbody>
            </table>
            <p>此郵件僅供稽核測試使用，簡訊並未實際發送。</p>
            <p>謝謝。</p>
        `;

        const toEmails =
            process.env.NODE_ENV === 'production'
                ? '<EMAIL>' // 實際的 Terry 的電子郵件
                : '<EMAIL>'; // 測試環境發送給自己

        try {
            await Utils.Mailer.send({
                to: [toEmails].toString(),
                cc: ['<EMAIL>'].toString(),
                subject:
                    (process.env.NODE_ENV === 'production' ? '' : '[TEST]') +
                    emailSubject,
                body: emailBody,
            });
            logger.info(`成功將彙整後的 ACH 簡訊內容發送至稽核郵件。`);
        } catch (error) {
            logger.error(
                `發送彙整 ACH 簡訊稽核郵件時發生錯誤: ${error.message}`,
            );
        } finally {
            // 清空已收集的簡訊內容
            BankACHService.collectedSmsMessages = [];
        }
    }

    private getBankInfo() {
        let bankCode = '';
        let bankAccount = '';
        let identity = '';
        switch (this.company) {
            case CLINICO:
                bankCode = configs.bank.clinico.code;
                bankAccount = configs.bank.clinico.account;
                identity = configs.bank.clinico.identity;
                break;
            case SKD:
                bankCode = configs.bank.skd.code;
                bankAccount = configs.bank.skd.account;
                identity = configs.bank.skd.identity;
                break;
            case IB:
                bankCode = configs.bank.ib.code;
                bankAccount = configs.bank.ib.account;
                identity = configs.bank.ib.identity;
                break;
        }
        return { bankCode, bankAccount, identity };
    }

    private genBankCode(params: { bankCode: string; bankBranchCode: string }) {
        let bankCode = params.bankCode;
        const regBankCode = new RegExp(/^\d{3}$/);
        if (!regBankCode.test(bankCode)) {
            return '';
        }
        const regBankBranchCode = new RegExp(/^\d{4}$/);
        if (regBankBranchCode.test(params.bankBranchCode)) {
            bankCode += params.bankBranchCode;
        }
        return stringUtil.paddingLeftForString(bankCode, 7, '0');
    }

    private async generateRequestFile(
        context: string,
        achDate: Date,
        isRepay: boolean,
    ): Promise<string> {
        const folderPath = `${configs.bank.exportACHFolder}/${this.company}`;
        if (!fs.existsSync(folderPath)) {
            mkdirp.sync(folderPath);
        }
        const ext = '.TXT';
        const fileName = `${this.company}-${isRepay ? 'RE-' : ''}ACH-${moment(
            achDate,
        ).format('YYYYMMDD')}${ext}`;
        const filePath = `${folderPath}/${fileName}`;
        await this.createFile(filePath, context);
        return filePath;
    }

    private async createFile(
        filePath: string,
        context: string,
    ): Promise<boolean> {
        return new Promise((resolve, reject) => {
            const file = fs.createWriteStream(filePath);
            file.write(context);
            file.end();
            file.on('finish', () => {
                resolve(true);
            });
            file.on('error', reject);
        });
    }

    private async getFeeDetail(params: {
        applicationId: string;
        period: number;
    }): Promise<Order | null> {
        return await this.orderService.findOneOrNullForACH({
            applicationId: params.applicationId,
            period: params.period,
        });
    }

    private getRespondMessage(code: string): string {
        let message = '';
        switch (code) {
            case '00':
                message = `(${code})成功扣款或入帳`;
                break;
            case '01':
                message = `(${code})存款不足(SD)`;
                break;
            case '02':
                message = `(${code})非委託用戶(SD)`;
                break;
            case '03':
                message = `(${code})已終止委託用戶(SD)`;
                break;
            case '04':
                message = `(${code})無此帳號(SC,SD)`;
                break;
            case '05':
                message = `(${code})收受者統編錯誤(SC,SD)`;
                break;
            case '06':
                message = `(${code})無此用戶號碼(SD)`;
                break;
            case '07':
                message = `(${code})用戶號碼不符(SD)`;
                break;
            case '08':
                message = `(${code})信用卡額度不足(SD)`;
                break;
            case '09':
                message = `(${code})未開卡(SD)`;
                break;
            case '10':
                message = `(${code})部分存款不足(SD651)`;
                break;
            case '11':
                message = `(${code})超過扣款限額(SD)`;
                break;
            case '12':
                message = `(${code})信用卡未續卡(SD)(108.03.04上線)`;
                break;
            case '13':
                message = `(${code})信用卡其他問題(SD)(108.03.04上線)`;
                break;
            case '22':
                message = `(${code})帳戶已結清(SC,SD)`;
                break;
            case '23':
                message = `(${code})靜止戶(SC,SD)`;
                break;
            case '24':
                message = `(${code})凍結戶(SC,SD)`;
                break;
            case '25':
                message = `(${code})帳戶存款遭法院強制執行(SC,SD)`;
                break;
            case '26':
                message = `(${code})警示戶(SC,SD)`;
                break;
            case '27':
                message = `(${code})該用戶已死亡(SC,SD)`;
                break;
            case '28':
                message = `(${code})發動行申請停止入扣帳(SC,SD)`;
                break;
            case '91':
                message = `(${code})請參考備註1(SC,SD)`;
                break;
            case '99':
            default:
                message = `(${code})其它(SC,SD)`;
                break;
        }
        return message;
    }

    /* 判斷是否產生交易檔 */
    async getACHDate(sendDate: Date) {
        //預計銀行入扣帳日(遇到假日要多天一起產生)
        const ACHDateArray: Date[] = [];
        const lastACHDate = await this.holidayService.afterBusinessDay(
            moment(sendDate).toDate(),
            2,
        );
        const perACHDate = await this.holidayService.afterBusinessDay(
            moment(sendDate).toDate(),
        );

        let currentDate = moment(lastACHDate);
        while (!currentDate.isSame(perACHDate)) {
            ACHDateArray.push(currentDate.toDate());
            currentDate = moment(currentDate).add(-1, 'day');
        }

        return {
            ACHDateArray,
            lastACHDate,
            perACHDate,
        };
    }
}
