import { AskingService } from '../asking.service';
import { ShipperType } from '../interfaces/order.interface';
import { BaseUseCase, DataValidatorHelper } from './shared';

/**
 * 處理食品付款回應的業務用例
 * 負責處理付款成功和失敗的後續作業
 *
 * 重構後使用共用工具來避免重複程式碼
 */
export class ProcessFoodResponseUseCase extends BaseUseCase {
    private askingService: AskingService;
    private dataValidatorHelper: DataValidatorHelper;

    constructor(company: string) {
        super(company);
        this.askingService = new AskingService(this.company);
        this.dataValidatorHelper = new DataValidatorHelper(company);
    }

    /**
     * 處理付款失敗的情況
     * @param authorizeRecord - 授權記錄
     * @param subscribeDetail - 訂閱明細
     * @returns Asking ID
     */
    async processPaymentFailure(
        authorizeRecord: any,
        subscribeDetail?: any,
    ): Promise<number | undefined> {
        return this.executeWithErrorHandling(
            async () => {
                // 驗證授權記錄
                if (
                    !this.dataValidatorHelper.validateOrderData(authorizeRecord)
                ) {
                    return undefined;
                }

                this.logInfo(
                    `Processing food payment failure for order: ${authorizeRecord.orderId}`,
                );

                let askingId: number | undefined;

                if (!subscribeDetail?.askingId) {
                    // 首次失敗，建立新的 Asking 案件
                    askingId = await this.createNewFailureAsking(
                        authorizeRecord,
                    );
                } else {
                    // 補刷失敗，更新現有 Asking 案件
                    await this.updateRepayFailureAsking(authorizeRecord);
                    askingId = subscribeDetail.askingId;
                }

                this.logWarn(
                    `Food payment failed: ${authorizeRecord.responseMessage} (#${askingId})`,
                );

                return askingId;
            },
            'process food payment failure',
            {
                orderId: authorizeRecord.orderId,
                responseMessage: authorizeRecord.responseMessage,
            },
        );
    }

    /**
     * 建立新的失敗 Asking 案件
     * @param authorizeRecord - 授權記錄
     * @returns Asking ID
     */
    private async createNewFailureAsking(
        authorizeRecord: any,
    ): Promise<number | undefined> {
        const askingId = await this.askingService.foodPayFailed({
            orderType: ShipperType.FOOD,
            orderId: authorizeRecord.orderId,
            period: authorizeRecord.paymentPeriod,
            errorMessage: authorizeRecord.responseMessage,
            storeCode: authorizeRecord.storeId,
        });

        this.logInfo(`New food payment failure asking created: #${askingId}`);

        return askingId;
    }

    /**
     * 更新補刷失敗 Asking 案件
     * @param authorizeRecord - 授權記錄
     */
    private async updateRepayFailureAsking(
        authorizeRecord: any,
    ): Promise<void> {
        await this.askingService.repayFoodFailed({
            orderId: authorizeRecord.orderId,
            cardNumber: authorizeRecord.cardNumber,
            errorMessage: authorizeRecord.responseMessage,
        });

        this.logInfo(
            `Food repay failure asking updated for order: ${authorizeRecord.orderId}`,
        );
    }

    /**
     * 處理補刷成功的情況
     * @param authorizeRecord - 授權記錄
     */
    async processRepaySuccess(authorizeRecord: any): Promise<void> {
        return this.executeWithErrorHandling(
            async () => {
                // 驗證授權記錄
                if (
                    !this.dataValidatorHelper.validateOrderData(authorizeRecord)
                ) {
                    return;
                }

                this.logInfo(
                    `Processing food repay success for order: ${authorizeRecord.orderId}`,
                );

                await this.askingService.repayFoodCompleted({
                    orderId: authorizeRecord.orderId,
                    cardNumber: authorizeRecord.cardNumber,
                });

                this.logInfo(
                    `Food repay success processed for order: ${authorizeRecord.orderId}`,
                );
            },
            'process food repay success',
            { orderId: authorizeRecord.orderId },
        );
    }

    /**
     * 判斷是否為付款成功
     * @param authorizeRecord - 授權記錄
     * @returns 是否成功
     */
    isPaymentSuccessful(authorizeRecord: any): boolean {
        return this.dataValidatorHelper.isPaymentSuccessful(
            authorizeRecord.respondCode,
        );
    }

    /**
     * 判斷是否為補刷案件
     * @param subscribeDetail - 訂閱明細
     * @returns 是否為補刷
     */
    isRepayCase(subscribeDetail?: any): boolean {
        return !!subscribeDetail?.askingId;
    }

    /**
     * 處理授權記錄的完整流程
     * @param authorizeRecord - 授權記錄
     * @param subscribeDetail - 訂閱明細
     * @returns 處理結果
     */
    async processAuthorizeRecord(
        authorizeRecord: any,
        subscribeDetail?: any,
    ): Promise<{
        isSuccess: boolean;
        askingId?: number;
    }> {
        const isSuccess = this.isPaymentSuccessful(authorizeRecord);
        const isRepay = this.isRepayCase(subscribeDetail);

        if (isSuccess) {
            // 付款成功
            if (isRepay) {
                // 補刷成功
                await this.processRepaySuccess(authorizeRecord);
            }
            // 一般成功不需要特別處理

            return { isSuccess: true };
        } else {
            // 付款失敗
            const askingId = await this.processPaymentFailure(
                authorizeRecord,
                subscribeDetail,
            );

            return { isSuccess: false, askingId };
        }
    }
}
