import {
    CreateShipperResult,
    UpdatePaymentParams,
} from './interfaces/payment.interface';
import * as moment from 'moment';
import * as _ from 'lodash';
import wpcHaDb from '../databases/tiptop.ha.database';
import wpcSkdDb from '../databases/tiptop.skd.database';
import wpcIbDb from '../databases/tiptop.ib.database';
import wpcDbUser1Db from '../databases/tiptop.dbuser1.database';
import {
    IUpdateShipperResult,
    Order,
    OrderType,
    OrderTypeResult,
    ShipperType,
    WPCFee,
    CreateNextPeriodParams,
    ISearchFee,
    IFoodOrderForm,
    ISearchVSubApp,
    ISearchSubscribeDetail,
    SearchOrderResult,
} from './interfaces/order.interface';
import logger from './logger.service';
import { CLINICO, SKD, IB } from '../const';
import {
    IUpdateSubscribeParams,
    ISubscribeDetail,
} from './interfaces/subscribe.interface';
import { SubscribeDetailModel } from '../models/subscribeDetail.model';

export class OrderService {
    private company: string;

    constructor(company: string) {
        this.company = company;
    }

    async findByDateForPay(shipDate?: Date): Promise<SearchOrderResult[]> {
        return shipDate
            ? this.search({ date: moment(shipDate).format('YYMMDD') })
            : this.search({ date: moment().format('YYMMDD') });
    }

    async findByDateForACH(shipDate: Date): Promise<Order[]> {
        return shipDate
            ? this.search({
                  isACH: true,
                  date: moment(shipDate).format('YYMMDD'),
              })
            : this.search({ isACH: true, date: moment().format('YYMMDD') });
    }

    async findByDateForPayByCard(shipDate?: Date): Promise<Order[]> {
        return shipDate
            ? this.search({
                  date: moment(shipDate).format('YYMMDD'),
                  isCard: true,
              })
            : this.search({ date: moment().format('YYMMDD'), isCard: true });
    }

    async findByDateForPayByACH(params: {
        achDate1: Date;
        achDate2: Date;
    }): Promise<Order[]> {
        return this.search({
            betweenDate: {
                date1: moment(params.achDate1).format('YYMMDD'),
                date2: moment(params.achDate2).format('YYMMDD'),
            },
            isACH: true,
        });
    }

    async findOneOrNull(params: {
        date: string;
        shipId: string;
    }): Promise<Order | null> {
        const order = await this.search({
            date: params.date,
            shipperId: params.shipId,
        });
        if (order.length != 1) {
            return null;
        }
        return order[0];
    }

    async findOneOrNullForACH(params: {
        applicationId: string;
        period: number;
    }): Promise<Order | null> {
        const order = await this.search({
            applicationId: params.applicationId,
            period: params.period,
            skipFeeState: true,
            isACH: true,
        });
        if (order.length != 1) {
            return null;
        }
        return order[0];
    }

    async findOneByApplicationPeriod(
        applicationId: string,
        period: number,
    ): Promise<Order | null> {
        const order = await this.search({
            applicationId,
            period,
            feeState: 'Y', //asking前已更新為Y
        });

        if (order.length != 1) {
            return null;
        }

        return order[0];
    }

    async search(params: ISearchFee): Promise<SearchOrderResult[]> {
        const result: SearchOrderResult[] = [];

        let sql = `select
        fee.PERIOD,
        fee.RSID,
        fee.FEEMETHOD,
        form.CCDID,
        form.IDENTITYCARD,
        form.EXPIREDDATE,
        fee.FEE,
        form.SOCODE,
        form.STARTDATE,
        form.ENDDATE,
        fee.FEESTATE,
        fee.LEGAL,
        form.STOREID,
        form.ITEMCODE,
        fee.FEEDATE,
        fee.SHIPID,
        fee.ACTIVE,
        form.SN,
        form.LINE,
        fee.DISCOUNTCODE, --折扣代碼
        fee.EXPECTEDFEE, --原價
        fee.TOTALPERIOD, --總期數
        form.ACHSTATUS,
        form.BANKCODE, 
        form.BANKBRANCHCODE, 
        form.BANKACCOUNT, 
        form.BANKIDENTITYCARD,
        form.BANKUSERNAME,
        form.MEMBERID,
        tc_lqa04 AS ITEM_LINE,
        tc_lqa05 AS ITEM_MATERIAL
        FROM rs_fee fee
        JOIN rs_application_form form ON fee.RSID = form.RSID
        LEFT JOIN tc_lqa_file ON form.RSID = tc_lqa01 -- 助聽器訂閱項目(一對多)
        WHERE 1 = 1
        AND fee.ACTIVE = 'Y'
        `;
        if (!params.skipFeeState) {
            if (params.feeState) {
                sql += ` AND fee.FEESTATE = '${params.feeState}' `;
            } else {
                sql += ` AND fee.FEESTATE = 'N' `;
            }
        }
        if (params.orderId) {
            sql += ` AND form.SOCODE = '${params.orderId}' `;
        }
        if (params.applicationId) {
            sql += ` AND fee.RSID = '${params.applicationId}' `;
        }
        if (params.date) {
            if (!moment(params.date, 'YYMMDD').isValid()) {
                throw new Error('Invalid date');
            }
            sql += ` AND fee.FEEDATE = TO_DATE('${params.date}','yymmdd') `;
        }
        if (params.betweenDate) {
            if (!moment(params.betweenDate.date1, 'YYMMDD').isValid()) {
                throw new Error('Invalid date1');
            }
            if (!moment(params.betweenDate.date2, 'YYMMDD').isValid()) {
                throw new Error('Invalid date2');
            }
            sql += ` AND fee.FEEDATE BETWEEN 
            TO_DATE('${params.betweenDate.date1}','yymmdd') AND TO_DATE('${params.betweenDate.date2}','yymmdd')`;
        }
        if (params.shipperId) {
            sql += ` AND fee.SHIPID = '${params.shipperId.trim()}' `;
        }
        if (params.isCard) {
            sql += ` AND fee.FEEMETHOD = '2' `;
        }
        if (params.isACH) {
            sql += ` AND fee.FEEMETHOD = '3' `;
        }
        if (params.period) {
            sql += ` AND fee.PERIOD = ${params.period}`;
        }

        try {
            let rows: any;

            switch (this.company) {
                case CLINICO:
                    rows = await wpcHaDb.raw(sql);
                    break;
                case SKD:
                    rows = await wpcSkdDb.raw(sql);
                    break;
                case IB:
                    rows = await wpcIbDb.raw(sql);
                    break;
            }

            const rowsWithCombinedKeys = rows.map((row) => ({
                ...row,
                combinedKey: `${row.RSID}_${row.PERIOD}`,
            }));

            const groupedData = _.groupBy(rowsWithCombinedKeys, 'combinedKey');

            for (const combinedKey in groupedData) {
                if (groupedData.hasOwnProperty(combinedKey)) {
                    const row = groupedData[combinedKey][0];

                    const processedItem: SearchOrderResult = {
                        period: row.PERIOD, // 期數
                        applicationId: row.RSID, // 申請書編號
                        method: row.FEEMETHOD, // 付款方式 1:現金 2:信用卡 3:銀行扣款
                        cardNumber: row.CCDID, // 卡號
                        personId: row.IDENTITYCARD, // 身分證字號
                        validThru: row.EXPIREDDATE, // 卡片到期日
                        amount: row.FEE, // 費用
                        orderId: row.SOCODE, // 訂單單號
                        startDate: row.STARTDATE, // 起始日
                        endDate: row.ENDDATE, // 到期日
                        isPaid: row.FEESTATE == 'Y', // 繳費狀態
                        company: row.LEGAL == 'HA' ? 'CLINICO' : row.LEGAL, // 公司編號
                        storeId: row.STOREID, // 門市編號
                        itemCode: row.ITEMCODE, // 料號
                        orderDate: row.FEEDATE, //出貨日期
                        shipperId: row.SHIPID, //出貨單號
                        active: row.ACTIVE,
                        SN: row.SN,
                        line: row.LINE,
                        isACH: row.ACHSTATUS == 'Y', // 啟用銀行扣款(ACH) N: 未通過 Y:已通過 P:送件中
                        bankCode: row.BANKCODE, // 銀行代號
                        bankBranchCode: row.BANKBRANCHCODE, // 分行代號
                        bankAccount: row.BANKACCOUNT, // 銀行帳號
                        bankIdentityCard: row.BANKIDENTITYCARD, //用戶身分證號
                        memberCode: row.MEMBERID,
                        items: [],
                    };

                    for (const item of groupedData[combinedKey]) {
                        processedItem.items.push({
                            line: item.ITEM_LINE,
                            materialCode: item.ITEM_MATERIAL,
                        });
                    }

                    result.push(processedItem);
                }
            }

            return result;
        } catch (err) {
            logger.error(`[${this.company}] 訂單查詢失敗: ${err} `);
            return [];
        }
    }

    /* 將交易結果傳更新到出貨單 */
    async updatePaymentStatus(order: UpdatePaymentParams) {
        const updateOgaFile = `update oga_file
        set TA_OGA09 = '${order.respondCode}', TA_OGA10 = '${order.authorizationCode}', TA_OGA11 = '${order.cardNumber}', TA_OGA12 = '${order.period}'
        where OGA01 = '${order.shipperId}'
        `;

        //ACH匯入結果時才更新狀態
        const feeState = order.respondCode == 'ACH' ? 'N' : 'Y';
        const updateRsFee = `update rs_fee
        set FEESTATE = '${feeState}', SHIPID = '${order.shipperId}'
        where RSID = '${order.applicationId}' and PERIOD = '${order.period}'
        `;

        switch (this.company) {
            case CLINICO:
                wpcHaDb.transaction(async function (trx) {
                    try {
                        await trx.raw(updateOgaFile);
                        await trx.raw(updateRsFee);
                        await trx.commit();
                    } catch (err) {
                        logger.error(
                            `[${order.applicationId}]交易結果更新TT失敗: ${err} `,
                        );
                    }
                });
                break;
            case SKD:
                wpcSkdDb.transaction(async function (trx) {
                    try {
                        await trx.raw(updateOgaFile);
                        await trx.raw(updateRsFee);
                        await trx.commit();
                    } catch (err) {
                        logger.error(
                            `[${order.applicationId}]交易結果更新TT失敗: ${err} `,
                        );
                    }
                });
                break;
            case IB:
                wpcIbDb.transaction(async function (trx) {
                    try {
                        await trx.raw(updateOgaFile);
                        await trx.raw(updateRsFee);
                        await trx.commit();
                    } catch (err) {
                        logger.error(
                            `[${order.applicationId}]交易結果更新TT失敗: ${err} `,
                        );
                    }
                });
                break;
        }

        await this.createNextPeriod(order);
    }

    async updateShipperIdToPaymentList(
        result: CreateShipperResult[],
    ): Promise<IUpdateShipperResult> {
        const info: IUpdateShipperResult = {
            total: 0,
            cc: 0,
            cash: 0,
            ACH: 0,
            free: 0,
        };
        for (const order of result) {
            const updateRsFee = `update rs_fee
            set SHIPID = '${order.shipperId}'
            where RSID = '${order.applicationId}' and PERIOD = '${order.period}'
            `;

            switch (this.company) {
                case CLINICO:
                    await wpcHaDb.raw(updateRsFee);
                    break;
                case SKD:
                    await wpcSkdDb.raw(updateRsFee);
                    break;
                case IB:
                    await wpcIbDb.raw(updateRsFee);
                    break;
            }

            if (order.amount == 0) {
                //0元交易
                await this.updatePaymentStatus({
                    shipperId: order.shipperId,
                    cardNumber: 'FREE',
                    applicationId: order.applicationId,
                    period: order.period,
                    respondCode: 'FREE',
                    authorizationCode: 'FREE',
                });
                info.free += 1;
            } else if (order.method == '1') {
                //將交易結果傳更新到出貨單(現金)
                await this.updatePaymentStatus({
                    shipperId: order.shipperId,
                    cardNumber: 'CASH',
                    applicationId: order.applicationId,
                    period: order.period,
                    respondCode: 'CASH',
                    authorizationCode: 'CASH',
                });
                info.cash += 1;
            } else if (order.method == '3') {
                //將交易結果傳更新到出貨單(銀行)
                await this.updatePaymentStatus({
                    shipperId: order.shipperId,
                    cardNumber: 'ACH',
                    applicationId: order.applicationId,
                    period: order.period,
                    respondCode: 'ACH',
                    authorizationCode: 'ACH',
                });
                info.ACH += 1;
            }
            info.total += 1;
        }
        info.cc = info.total - info.cash - info.ACH;

        return info;
    }

    async checkShipperId(order: Order): Promise<string | null> {
        let shipper = null;
        const cmd = `select SHIPID from rs_fee
            where RSID = '${order.applicationId}' and PERIOD = '${order.period}'`;
        switch (this.company) {
            case CLINICO: {
                const wpcHaDbRows = await wpcHaDb.raw(cmd);
                shipper = wpcHaDbRows[0].SHIPID;
                break;
            }
            case SKD: {
                const wpcSkdDbRows = await wpcSkdDb.raw(cmd);
                shipper = wpcSkdDbRows[0].SHIPID;
                break;
            }
            case IB: {
                const wpcIbDbRows = await wpcIbDb.raw(cmd);
                shipper = wpcIbDbRows[0].SHIPID;
                break;
            }
        }
        return shipper;
    }

    private async getOrderHeader(): Promise<OrderType> {
        //TODO: 等泰哥資料表重構完再改成接資料庫
        // let rows: any = {};
        // let cmd = `SELECT * FROM tc_lpz_file`;
        // switch (this.company) {
        //     case CLINICO:
        //         rows = await wpcHaDb.raw(cmd);
        //         break;
        //     case SKD:
        //         rows = await wpcSkdDb.raw(cmd);
        //         break;
        //     case IB:
        //         rows = await wpcIbDb.raw(cmd);
        //         break;
        // }
        const orderType: OrderType = {
            rsOrder: 'ASR01', //rows[0].TC_LPZ01,
            rsApplication: 'RSHA1', //rows[0].TC_LPZ02,
            rsShipper: 'RS501', //rows[0].TC_LPZ07,
            haOrder: 'ASH01', //rows[0].TC_LPZ09,
            haApplication: 'HARS1', //rows[0].TC_LPZ10,
            haShipper: 'HA501', //rows[0].TC_LPZ11,
            rsMaskOrder: 'ASR02', //rows[0].TC_LPZ21, //訂單
            rsMaskApplication: 'RSHA2', //rows[0].TC_LPZ22, //申請書
            rsMaskShipper: 'RS502', //rows[0].TC_LPZ23, //出貨單
            ccFoodShipper: 'ASF01', //rows[0].TC_OPY09, // 食品訂閱制訂單
            ccFoodOrder: '',
            ccFoodApplication: '',
        };
        return orderType;
    }

    async getOderTypeByShipperType(
        header: string,
    ): Promise<ShipperType | null> {
        const orderType = await this.getOrderHeader();
        switch (header) {
            case orderType.haShipper:
                return ShipperType.HA;
            case orderType.rsShipper:
                return ShipperType.RS;
            case orderType.rsMaskShipper:
                return ShipperType.RSM;
            case orderType.ccFoodShipper:
                return ShipperType.FOOD;
        }
        return null;
    }

    async getOderTypeByApplicationType(
        header: string,
    ): Promise<ShipperType | null> {
        const orderType = await this.getOrderHeader();
        switch (header) {
            case orderType.haApplication:
                return ShipperType.HA;
            case orderType.rsApplication:
            case 'RSWL1':
            case 'RSIB1':
                return ShipperType.RS;
            case orderType.rsMaskApplication:
                return ShipperType.RSM;
        }
        return null;
    }

    async getOderTypeHearder(type: string): Promise<OrderTypeResult | null> {
        const orderType = await this.getOrderHeader();
        switch (type) {
            case ShipperType.HA:
                return {
                    type: ShipperType.HA,
                    order: orderType.haOrder,
                    application: orderType.haApplication,
                    shipPrefixCode: orderType.haShipper,
                };
            case ShipperType.RS:
                return {
                    type: ShipperType.RS,
                    order: orderType.rsOrder,
                    application: orderType.rsApplication,
                    shipPrefixCode: orderType.rsShipper,
                };
            case ShipperType.RSM:
                return {
                    type: ShipperType.RSM,
                    order: orderType.rsMaskOrder,
                    application: orderType.rsMaskApplication,
                    shipPrefixCode: orderType.rsMaskShipper,
                };
        }
        return null;
    }

    async createNextPeriod(order: CreateNextPeriodParams): Promise<void> {
        try {
            const thisPeriod = await this.getFeeInfo({
                applicationId: order.applicationId,
                period: order.period,
            });

            if (!thisPeriod) {
                throw new Error(
                    `Fee not found(${order.applicationId},${order.period})`,
                );
            }

            const nextPeriod = await this.getFeeInfo({
                applicationId: order.applicationId,
                period: order.period + 1,
            });

            //判斷是否已新增下一期
            if (nextPeriod) {
                return;
            }

            //判斷是否已達最大期數
            if (thisPeriod.TOTALPERIOD == thisPeriod.PERIOD) {
                return;
            }

            const firstPeriod = await this.getFeeInfo({
                applicationId: order.applicationId,
                period: 1,
            });

            if (!firstPeriod) {
                throw new Error(
                    `first period not found(${order.applicationId})`,
                );
            }

            const sql = `INSERT INTO RS_FEE (RSID, PERIOD, FEEDATE, FEE, FEESTATE, FEEMETHOD, TOTALPERIOD, EXPECTEDFEE, ACTIVE, CREATEDATE, CREATETIME, STOREID, LEGAL) 
            VALUES(
            '${thisPeriod.RSID}', 
            ${thisPeriod.PERIOD + 1}, 
            TO_DATE('${moment(firstPeriod.FEEDATE)
                .add(thisPeriod.PERIOD, 'month')
                .format('YYYY-MM-DD')}', 'YYYY-MM-DD') , 
            ${
                thisPeriod.EXPECTEDFEE ? thisPeriod.EXPECTEDFEE : thisPeriod.FEE
            }, 
            'N', 
            '${thisPeriod.FEEMETHOD}', 
            ${thisPeriod.TOTALPERIOD ?? null}, 
            ${thisPeriod.EXPECTEDFEE ?? null}, 
            'Y', 
            TO_DATE('${moment().format('YYYY-MM-DD')}', 'YYYY-MM-DD'), 
            '${moment().format('HH:mm:ss')}', 
            '${thisPeriod.STOREID}', 
            '${thisPeriod.LEGAL}')`;

            switch (this.company) {
                case CLINICO:
                    await wpcHaDb.raw(sql);
                    break;
                case SKD:
                    await wpcSkdDb.raw(sql);
                    break;
                case IB:
                    await wpcIbDb.raw(sql);
                    break;
            }
        } catch (err) {
            logger.error(`[${order.applicationId}]下期收款檔新增失敗: ${err} `);
        }
    }

    private async getFeeInfo(order: {
        applicationId: string;
        period: number;
    }): Promise<WPCFee | null> {
        let result: WPCFee | null = null;

        const cmd = `
            SELECT * FROM rs_fee
            WHERE RSID = '${order.applicationId}' 
            AND PERIOD = '${order.period}'
        `;

        switch (this.company) {
            case CLINICO:
                const wpcHaDbRows = await wpcHaDb.raw(cmd);
                result = wpcHaDbRows.length > 0 ? <WPCFee>wpcHaDbRows[0] : null;
                break;
            case SKD:
                const wpcSkdDbRows = await wpcSkdDb.raw(cmd);
                result =
                    wpcSkdDbRows.length > 0 ? <WPCFee>wpcSkdDbRows[0] : null;
                break;
            case IB:
                const wpcIbDbRows = await wpcIbDb.raw(cmd);
                result = wpcIbDbRows.length > 0 ? <WPCFee>wpcIbDbRows[0] : null;
                break;
        }

        return result;
    }

    async checkFoodShipperID(order: IFoodOrderForm): Promise<string | null> {
        let shipperID = null;
        const sql = `select RS_ID from V_SUB_APPLICATIONS where RS_ID = '${order.rsId}' and PAYMENT_PERIOD = ${order.period}`;
        let rows: any;

        try {
            rows = await wpcDbUser1Db.raw(sql);
        } catch (e) {
            console.log(e);
        }

        shipperID = rows[0] ? rows[0].ORDER_ID : null;
        return shipperID;
    }

    async searchFood(
        params: ISearchVSubApp,
        cardDate?: Date,
    ): Promise<IFoodOrderForm[]> {
        const result: IFoodOrderForm[] = [];
        const estimatedCardDate = moment(cardDate).format('YYYY-MM-DD');

        let sql = `select vsa.COMPANY,
                                  vsa.RS_ID,
                                  vsa.NEXT_SHIPPING_DATE,
                                  vsa.REG_ORDER_DAY,
                                  MAX(vsa.PAYMENT_PERIOD) as PAYMENT_PERIOD,
                                  MAX(vsa.ORDER_SEQ_ID) as ORDER_SEQ_ID,
                                  vsa.CCDID,
                                  vsa.EXPIRED_DATE,
                                  vsa.MEMBER_ID,
                                  vsa.MEMBER_NAME,
                                  vsa.STORE_ID,
                                  vsa.STORE_NAME,
                                  vsa.USER_ID,
                                  vsa.RS_OUTSTANDING_AMOUNT,
                                  vsa.LEGAL
                           from V_SUB_APPLICATIONS vsa 
                           where vsa.COMPANY = '${this.company}'`;

        if (params.nextShippingDate)
            sql += ` and vsa.NEXT_SHIPPING_DATE = date '${params.nextShippingDate}'`;

        if (params.estimatedShippingDate)
            sql += ` and vsa.ESTIMATED_SHIPPING_DATE = date '${params.estimatedShippingDate}'`;

        if (params.estimatedCardDate) {
            sql += ` and vsa.ESTIMATED_CARD_DATE = date '${params.estimatedCardDate}'`;
        }

        if (params.isRepay) {
            sql += ` and vsa.ASKING_ID is not null and vsa.ORDER_ID is null`;
        }

        if (params.orderId) sql += ` and vsa.ORDER_ID = '${params.orderId}'`;

        if (params.rsId) sql += ` and vsa.RS_ID = '${params.rsId}'`;

        sql += ` group by vsa.COMPANY, vsa.RS_ID, vsa.NEXT_SHIPPING_DATE, vsa.REG_ORDER_DAY, 
         vsa.CCDID, vsa.EXPIRED_DATE, vsa.MEMBER_ID, vsa.MEMBER_NAME, vsa.STORE_ID, vsa.STORE_NAME, vsa.USER_ID, vsa.RS_OUTSTANDING_AMOUNT, vsa.LEGAL`;
        if (params.isRepay) {
            sql += `, vsa.ASKING_ID`;
        }
        try {
            let rows: any;

            console.log(sql);

            rows = await wpcDbUser1Db.raw(sql);

            for (const row of rows) {
                result.push({
                    company: row.COMPANY, // 公司別
                    orderDate: estimatedCardDate, // 訂單日
                    rsId: row.RS_ID, //申請書編號
                    nextShippingDate: row.NEXT_SHIPPING_DATE, // 下次出貨日
                    regOrderDay: row.REG_ORDER_DAY, // 定期訂購日
                    period: row.PAYMENT_PERIOD, // 期數
                    orderSeqId: row.ORDER_SEQ_ID, // 項次
                    cardNumber: row.CCDID, // 卡號
                    validThru: row.EXPIRED_DATE, // 有效日期 YYMM、以卡片上有效日期為準則
                    memberCode: row.MEMBER_ID,
                    memberName: row.MEMBER_NAME,
                    storeId: row.STORE_ID, // 門市編號
                    storeName: row.STORE_NAME, // 門市名稱
                    amount: row.RS_OUTSTANDING_AMOUNT,
                    userId: row.USER_ID,
                    legal: row.LEGAL,
                    isRepay: params.isRepay,
                });
            }
            return result;
        } catch (err) {
            logger.error(`[${this.company}] 訂單查詢失敗: ${err} `);
            return [];
        }
    }

    async searchSubscribeDetail(
        params: ISearchSubscribeDetail,
    ): Promise<SubscribeDetailModel[]> {
        const subscribeDetailRecords: SubscribeDetailModel[] = [];
        let wpcDB;
        let dbName = '';
        switch (this.company) {
            case CLINICO:
                wpcDB = await wpcHaDb;
                dbName = 'HA';
                break;
            case SKD:
                wpcDB = await wpcSkdDb;
                dbName = 'SKD';
                break;
            case IB:
                wpcDB = await wpcIbDb;
                dbName = 'IB';
                break;
        }

        let sql = `SELECT * FROM ${dbName}.SUBSCRIBE_DETAIL WHERE 1 = 1`;

        if (params.rsId) sql += ` AND RS_ID = '${params.rsId}'`;

        if (params.period) sql += ` AND PAYMENT_PERIOD = ${params.period}`;

        if (params.estimatedCardDate) {
            const estimatedCardDate: string = moment(
                params.estimatedCardDate,
            ).format('YYYY-MM-DD');
            sql += ` AND ESTIMATED_CARD_DATE = date '${estimatedCardDate}'`;
        }

        if (params.estimatedShippingDate) {
            const estimatedShippingDate: string = moment(
                params.estimatedShippingDate,
            ).format('YYYY-MM-DD');
            sql += ` AND ESTIMATED_SHIPPING_DATE = date '${estimatedShippingDate}'`;
        }

        if (params.orderId) sql += ` AND ORDER_ID like '%${params.orderId}'`;

        if (params.shippingId)
            sql += ` AND SHIPPING_ID = '${params.shippingId}'`;

        if (params.isProcessed !== undefined) {
            const isCard = params.isProcessed ? 'Y' : 'N';
            sql += ` AND IS_CARD = '${isCard}'`;
        }

        if (params.isCompleted !== undefined) {
            const isCompleted = params.isCompleted ? 'Y' : 'N';
            sql += ` AND IS_CARD_FAILED = '${isCompleted}'`;
        }

        if (params.isReCard !== undefined) {
            const isReCard = params.isReCard ? 'Y' : 'N';
            sql += ` AND IS_RE_CARD = '${isReCard}'`;
        }

        const rows = await wpcDB.raw(sql);

        for (const row of rows) {
            subscribeDetailRecords.push({
                estimatedCardDate: row.ESTIMATED_CARD_DATE,
                estimatedShippingDate: row.ESTIMATED_SHIPPING_DATE,
                expectedCardAmount: row.EXPECTED_CARD_AMOUNT,
                isProcessed: row.IS_CARD,
                isCompleted: row.IS_CARD_FAILED,
                isReCard: row.IS_RE_CARD,
                legal: row.LEGAL,
                orderSeqId: row.ORDER_SEQ_ID,
                paymentPeriod: row.PAYMENT_PERIOD,
                orderId: row.ORDER_ID,
                rsId: row.RS_ID,
                storeId: row.STORE_ID,
                askingId: row.ASKING_ID,
            });
        }

        return subscribeDetailRecords;
    }

    async createSubscribeDetail(subscribeDetail: ISubscribeDetail) {
        let wpcDB;
        let dbName = '';

        switch (subscribeDetail.company) {
            case CLINICO:
                wpcDB = await wpcHaDb;
                dbName = 'HA';
                break;
            case SKD:
                wpcDB = await wpcSkdDb;
                dbName = 'SKD';
                break;
            case IB:
                wpcDB = await wpcIbDb;
                dbName = 'IB';
                break;
        }

        let sql = `INSERT INTO ${dbName}.SUBSCRIBE_DETAIL (RS_ID, ORDER_SEQ_ID, PAYMENT_PERIOD, ESTIMATED_CARD_DATE, `;
        sql += `ESTIMATED_SHIPPING_DATE, PAYMENT_TYPE, EXPECTED_CARD_AMOUNT, ORDER_ID, PAYMENT_REMARK, `;
        sql += `CREATED_BY, CREATED_GROUP, CREATED_DATE, CREATED_TIME, STORE_ID, LEGAL)`;
        sql += `VALUES ('${subscribeDetail.rsId}', ${subscribeDetail.orderSeqId}, ${subscribeDetail.paymentPeriod}, `;
        sql += `TO_DATE('${subscribeDetail.estimatedCardDate}', 'YYYY-MM-DD'), `;
        sql += `TO_DATE('${subscribeDetail.estimatedShippingDate}', 'YYYY-MM-DD'), `;
        sql += `'${subscribeDetail.paymentType}', ${subscribeDetail.expectedCardAmount}, '${subscribeDetail.orderId}', `;
        sql += `'${subscribeDetail.paymentRemark}', '${subscribeDetail.createdBy}', '${subscribeDetail.createdGroup}', `;
        sql += `TO_DATE('${subscribeDetail.createdDate}', 'YYYY-MM-DD'), '${subscribeDetail.createdTime}', `;
        sql += `'${subscribeDetail.storeId}', '${subscribeDetail.legal}')`;
        console.log(sql);

        // 更新下次出貨日，日要以 定期訂購日 為主
        let nextShipDate = moment(subscribeDetail.nextShippingDate)
            .add(2, 'months')
            .format('YYYY-MM');
        nextShipDate = `${nextShipDate}-${subscribeDetail.regOrderDay
            .toString()
            .padStart(2, '0')}`;
        nextShipDate = await this.getLastValidDate(nextShipDate);
        const updateSql = `update ${dbName}.tc_opa_file set tc_opa09 = date '${nextShipDate}' where tc_opa01 = '${subscribeDetail.rsId}'`;

        const trx = await wpcDB.transaction();

        try {
            await trx.raw(sql).timeout(5000);

            console.log(updateSql);
            await trx.raw(updateSql).timeout(5000);
            await trx.commit();
        } catch (e) {
            logger.error(e.message);
            await trx.rollback();
        }
    }

    async getOneSubscribeDetailByOrderID(
        orderId: string,
    ): Promise<SubscribeDetailModel | null> {
        const data = await this.searchSubscribeDetail({ orderId: orderId });
        if (data) return data[0];

        return null;
    }

    async updateSubscribeCardStateByOrderId(
        orderId: string,
        params: IUpdateSubscribeParams,
    ) {
        let wpcDB;
        let dbName = '';

        switch (this.company) {
            case CLINICO:
                wpcDB = await wpcHaDb;
                dbName = 'HA';
                break;
            case SKD:
                wpcDB = await wpcSkdDb;
                dbName = 'SKD';
                break;
            case IB:
                wpcDB = await wpcIbDb;
                dbName = 'IB';
                break;
        }
        let sql = `UPDATE ${dbName}.SUBSCRIBE_DETAIL SET`;
        if (params.isProcessed !== undefined) {
            const isCard = params.isProcessed == 'Y' ? 'Y' : 'N';
            sql += ` IS_CARD = '${isCard}',`;
        }
        if (params.isCompleted !== undefined) {
            const isCompleted = params.isCompleted ? 'Y' : 'N';
            sql += ` IS_CARD_FAILED = '${isCompleted}',`;
        }
        if (params.isReCard !== undefined) {
            const isReCard = params.isReCard ? 'Y' : 'N';
            sql += ` IS_RE_CARD = '${isReCard}',`;
        }
        if (params.shippingId !== undefined) {
            const shippingId = params.shippingId;
            sql += ` SHIPPING_ID = '${shippingId}',`;
        }
        if (params.askingId !== undefined) {
            const askingId = params.askingId;
            sql += ` ASKING_ID = '${askingId}',`;
        }
        if (params.bankRespondCode !== undefined) {
            sql += ` PROCESS_STATUS = '${params.bankRespondCode}',`;
        }
        if (sql.endsWith(',')) {
            sql = sql.slice(0, -1);
        }

        sql += ` WHERE ORDER_ID = '${orderId}'`;

        const trx = await wpcDB.transaction();
        try {
            await trx.raw(sql).timeout(5000);

            await trx.commit();
        } catch (e) {
            logger.error(e.message);
            await trx.rollback();
        }
    }

    async updateSubscribeOrderId(
        rsId: string,
        orderSeqId: number,
        orderId: string,
    ) {
        let wpcDB;
        let dbName = '';

        switch (this.company) {
            case CLINICO:
                wpcDB = await wpcHaDb;
                dbName = 'HA';
                break;
            case SKD:
                wpcDB = await wpcSkdDb;
                dbName = 'SKD';
                break;
            case IB:
                wpcDB = await wpcIbDb;
                dbName = 'IB';
                break;
        }
        let sql = `UPDATE ${dbName}.SUBSCRIBE_DETAIL SET ORDER_ID = '${orderId}' `;

        sql += ` WHERE RS_ID = '${rsId}' AND ORDER_SEQ_ID = '${orderSeqId}'`;

        const trx = await wpcDB.transaction();
        try {
            await trx.raw(sql).timeout(5000);

            await trx.commit();
        } catch (e) {
            logger.error(e.message);
            await trx.rollback();
        }
    }

    async getLastValidDate(dateString: string): Promise<string> {
        const date = moment(dateString, 'YYYY-MM-DD', true); // 使用嚴格模式解析日期

        // 檢查日期是否有效
        if (date.isValid()) {
            return date.format('YYYY-MM-DD'); // 如果日期有效，返回原日期
        } else {
            // 如果日期無效，取得當月的最後一天日期
            const lastDayOfMonth = moment(dateString, 'YYYY-MM').endOf('month');
            return lastDayOfMonth.format('YYYY-MM-DD');
        }
    }
}
