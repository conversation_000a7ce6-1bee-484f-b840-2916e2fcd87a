import { IGenerateAuthorizeResponseResult } from '../interfaces/esun.interface';
import { BaseUseCase } from './shared/base.usecase';
import { DataValidatorHelper } from './shared/dataValidator.helper';
import { EsunServiceHelper } from './shared/esunService.helper';
import { FileManagerHelper } from './shared/fileManager.helper';

/**
 * 處理信用卡付款的業務用例
 * 負責信用卡授權請求的產生、上傳和處理
 *
 * 重構後使用共用工具來避免重複程式碼
 */
export class ProcessCardPaymentUseCase extends BaseUseCase {
    private esunServiceHelper: EsunServiceHelper;
    private fileManagerHelper: FileManagerHelper;
    private dataValidatorHelper: DataValidatorHelper;

    constructor(company: string) {
        super(company);
        this.esunServiceHelper = new EsunServiceHelper(company);
        this.fileManagerHelper = new FileManagerHelper(company);
        this.dataValidatorHelper = new DataValidatorHelper(company);
    }

    /**
     * 處理信用卡付款請求
     * @param orders - 訂單列表
     * @param isRepay - 是否為補刷卡
     * @param logPrefix - 日誌前綴
     */
    async execute(
        orders: any[],
        isRepay: boolean,
        logPrefix: string,
    ): Promise<void> {
        return this.executeWithErrorHandling(
            async () => {
                // 檢查是否有訂單需要處理
                if (
                    this.dataValidatorHelper.isEmptyArray(
                        orders,
                        logPrefix.toLowerCase(),
                    )
                ) {
                    return;
                }

                this.logInfo(
                    `Processing ${orders.length} ${logPrefix.toLowerCase()}`,
                );

                // 1. 產生授權請求檔案
                await this.esunServiceHelper.generateAuthorizationRequest(
                    orders,
                    isRepay,
                );

                // 2. 備份上傳檔案
                await this.fileManagerHelper.backupUploadFiles();

                // 3. 執行上傳
                await this.esunServiceHelper.uploadToEsun();

                this.logInfo(`${logPrefix} processing completed successfully`);
            },
            `process card payment (${logPrefix})`,
            { orderCount: orders.length, isRepay, logPrefix },
        );
    }

    /**
     * 處理玉山回應檔案
     * @param shipDate - 出貨日期
     * @param isRepay - 是否為補刷卡
     * @returns 回應結果或 null（如果沒有檔案需要處理）
     */
    async processResponse(
        shipDate: Date | undefined,
        isRepay: boolean,
    ): Promise<IGenerateAuthorizeResponseResult | null> {
        return this.executeWithErrorHandling(
            () => this.esunServiceHelper.processResponse(shipDate, isRepay),
            'process Esun response',
            { shipDate, isRepay },
        );
    }
}
