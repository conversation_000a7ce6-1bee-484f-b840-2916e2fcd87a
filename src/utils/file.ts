import * as path from 'path';
import * as fs from 'fs';
import * as mkdirp from 'mkdirp';
import configs from '../configs';
import { Helpers } from '@clinico/clinico-node-framework';
import logger from '../services/logger.service';

class FileUtil {
    async backupUploadFiles(company: string) {
        const uploadBackUpFolder = `${configs.esun.uploadBackUpFolder}/${company}`;
        if (!fs.existsSync(uploadBackUpFolder)) {
            mkdirp.sync(uploadBackUpFolder);
        }
        const uploadFolder = `${configs.esun.uploadFolder}/${company}`;
        const filePaths = this.getFilePaths(uploadFolder);
        for (const filePath of filePaths) {
            let index = 1;
            const fileName = path.basename(filePath);
            let backupFileName = `${company}-${fileName}`;
            while (
                Helpers.File.isExist(`${uploadBackUpFolder}/${backupFileName}`)
            ) {
                backupFileName = `${company}-${index++}-${fileName}`;
            }

            fs.copyFileSync(
                filePath,
                `${uploadBackUpFolder}/${backupFileName}`,
            );
        }
    }

    async backupNotUploadFiles(company: string) {
        const uploadBackUpFolder = `${configs.esun.uploadBackUpFolder}/${company}`;
        if (!fs.existsSync(uploadBackUpFolder)) {
            mkdirp.sync(uploadBackUpFolder);
        }
        const uploadFolder = `${configs.esun.uploadFolder}/${company}`;
        const filePaths = this.getFilePaths(uploadFolder);
        for (const filePath of filePaths) {
            let index = 1;
            const fileName = path.basename(filePath);
            let backupFileName = `${company}-${fileName}`;
            while (
                Helpers.File.isExist(`${uploadBackUpFolder}/${backupFileName}`)
            ) {
                backupFileName = `${company}-${index++}-${fileName}`;
            }

            await Helpers.File.move(
                `${filePath}`,
                `${uploadBackUpFolder}/${backupFileName}`,
                true,
            );

            logger.warn(`'${fileName}' Not upload!`);
        }
    }

    getFilePaths(fileFolder: string): string[] {
        const filePaths: string[] = [];
        const files = fs.readdirSync(fileFolder);
        files.forEach(function (item, index) {
            const filePath = path.join(fileFolder, item);
            const stat = fs.statSync(filePath);
            if (stat.isFile()) {
                filePaths.push(filePath);
            }
        });

        return filePaths;
    }

    async backupDownloadFiles(company: string, fileName: string) {
        const downloadBackUpFolder = configs.esun.downloadBackUpFolder;
        if (!fs.existsSync(downloadBackUpFolder)) {
            mkdirp.sync(downloadBackUpFolder);
        }
        let index = 1;
        let backupFileName = `${company}-${fileName}`;
        while (
            Helpers.File.isExist(`${downloadBackUpFolder}/${backupFileName}`)
        ) {
            backupFileName = `${company}-${index++}-${fileName}`;
        }
        await Helpers.File.move(
            `${path.resolve(
                configs.esun.downloadFolder,
            )}/${company}/${fileName}`,
            `${configs.esun.downloadBackUpFolder}/${backupFileName}`,
            true,
        );
    }

    async checkFile(path: string): Promise<boolean> {
        for (let times = 1; times <= 3; times++) {
            console.log(`waiting for download file (5s), times: ${times}`);
            await this.delay(5000);
            if (fs.existsSync(path)) {
                return true;
            }
        }

        return false;
    }

    async backupACHFiles(company: string) {
        const uploadBackUpFolder = `${configs.bank.backupACHFolder}/${company}`;
        if (!fs.existsSync(uploadBackUpFolder)) {
            mkdirp.sync(uploadBackUpFolder);
        }
        const uploadFolder = `${configs.bank.exportACHFolder}/${company}`;
        const filePaths = this.getFilePaths(uploadFolder);
        for (const filePath of filePaths) {
            let index = 1;
            const fileName = path.basename(filePath);
            let backupFileName = `${company}-${fileName}`;
            while (
                Helpers.File.isExist(`${uploadBackUpFolder}/${backupFileName}`)
            ) {
                backupFileName = `${company}-${index++}-${fileName}`;
            }
            await Helpers.File.move(
                filePath,
                `${uploadBackUpFolder}/${backupFileName}`,
                false,
            );
        }
    }

    private async delay(ms: number) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }
}

const fileUtil = new FileUtil();
export default fileUtil;
