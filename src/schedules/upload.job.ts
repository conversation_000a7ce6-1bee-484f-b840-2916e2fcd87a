import logger from '../services/logger.service';
import { TaskService } from '../services/task.service';
import { FoodTaskService } from '../services/foodTask.service';

export default class uploadJob {
    private company: string;
    constructor(company: string) {
        this.company = company;
    }
    async run(): Promise<void> {
        try {
            const taskService = new TaskService(this.company);
            await taskService.upload();
        } catch (err) {
            logger.error(err.message);
        }
    }

    async repayRun(): Promise<void> {
        try {
            const taskService = new TaskService(this.company);
            await taskService.repayUpload();
        } catch (err) {
            logger.error(err.message);
        }
    }

    async reRun(shipDate: Date): Promise<void> {
        try {
            const taskService = new TaskService(this.company);
            await taskService.upload(shipDate);
        } catch (err) {
            logger.error(err.message);
        }
    }

    async foodRun(): Promise<void> {
        try {
            const taskService = new FoodTaskService(this.company);
            await taskService.upload();
        } catch (err) {
            logger.error(err.message);
        }
    }

    // async foodRepayRun():Promise<void> {
    //     try {
    //         const taskService = new FoodTaskService(this.company);
    //         await taskService.repayUpload();
    //     } catch (err) {
    //         logger.error(err.message);
    //     }
    // }
}

process.on('unhandledRejection', console.dir);
