import wpcHaDb from '../databases/tiptop.ha.database';
import wpcSkdDb from '../databases/tiptop.skd.database';
import wpcIbDb from '../databases/tiptop.ib.database';
import logger from './logger.service';
import { CLINICO, SKD, IB } from '../const';
import {
    ISearchRepay,
    ICreateRepayParams,
    RepayOrder,
    IUpdateRepayParams,
    IUpdateRepayProcessParams,
    EnumPayFailedType,
} from './interfaces/repay.interface';
import * as moment from 'moment';
import { EnumPayStateType } from './interfaces/common.interface';

export class RepayService {
    private company: string;
    constructor(company: string) {
        this.company = company;
    }

    async findOneOrNull(params: ISearchRepay): Promise<RepayOrder | null> {
        const order = await this.search({
            feeDate: params.feeDate,
            shipId: params.shipId,
            applicationId: params.applicationId,
            period: params.period,
            isProcess: params.isProcess,
        });
        if (order.length != 1) {
            return null;
        }
        return order[0];
    }

    async findOneOrNullForProcessACH(params: {
        applicationId: string;
        period: number;
    }): Promise<RepayOrder | null> {
        const order = await this.search({
            applicationId: params.applicationId,
            period: params.period,
            isACH: true,
            isProcess: true,
        });
        if (order.length != 1) {
            return null;
        }
        return order[0];
    }

    async getPayFailedType(
        applicationCode: string,
    ): Promise<EnumPayFailedType> {
        const repay = await this.search({
            applicationId: applicationCode,
            state: EnumPayStateType.Unpaid,
        });
        switch (repay.length) {
            case 0:
            case 1:
                return EnumPayFailedType.Normal;
            case 2:
                return EnumPayFailedType.ThreeConsecutive2Periods;
            case 3:
                return EnumPayFailedType.ThreeConsecutive3Periods;
            case 4:
                return EnumPayFailedType.ThreeConsecutive4Periods;
            default:
                return EnumPayFailedType.FourConsecutivePeriods;
        }
    }

    async search(params: ISearchRepay): Promise<RepayOrder[]> {
        const result: RepayOrder[] = [];

        let sql = `SELECT
        fee.PERIOD,
        fee.APPLICATION_CODE,
        fee.METHOD,   
        fee.FEE,
        fee.FEE_DATE,
        fee.STATE,
        fee.LEGAL,
        fee.SHIP_CODE,
        fee.ACTIVE,
        fee.ASKING_ID,
        form.SOCODE,
        form.STARTDATE,
        form.ENDDATE,
        form.CCDID,
        form.IDENTITYCARD,
        form.EXPIREDDATE,
        form.STOREID,
        form.ITEMCODE,
        form.SN,
        form.LINE,
        form.ACHSTATUS,
        form.BANKCODE, 
        form.BANKBRANCHCODE, 
        form.BANKACCOUNT, 
        form.BANKIDENTITYCARD,
        form.BANKUSERNAME,
        form.MEMBERID
        FROM RS_FEE_REPAY fee
        JOIN RS_APPLICATION_FORM form ON fee.APPLICATION_CODE = form.RSID
        WHERE 1 = 1 
        AND fee.ACTIVE = 'Y' `;

        if (params.shipId) {
            sql += ` AND fee.SHIP_CODE = '${params.shipId}' `;
        }
        if (params.applicationId) {
            sql += ` AND fee.APPLICATION_CODE = '${params.applicationId}' `;
        }
        if (params.period) {
            sql += ` AND fee.PERIOD = ${params.period} `;
        }
        if (params.state) {
            sql += ` AND fee.STATE = '${params.state}' `;
        }
        if (params.feeDate) {
            sql += ` AND fee.FEE_DATE = TO_DATE('${moment(
                params.feeDate,
            ).format('YYYY-MM-DD')}','YYYY-MM-DD') `;
        }
        if (params.isCard) {
            sql += ` AND fee.METHOD = '2' `;
        }
        if (params.isACH) {
            sql += ` AND fee.METHOD = '3' `;
        }
        if (params.isProcess) {
            sql += ` AND fee.IS_PROCESS = 'Y' `;
        } else {
            sql += ` AND (fee.IS_PROCESS != 'Y' OR fee.IS_PROCESS is NULL) `;
        }
        if (params.betweenDate) {
            if (!moment(params.betweenDate.date1, 'YYMMDD').isValid()) {
                throw new Error('Invalid date1');
            }
            if (!moment(params.betweenDate.date2, 'YYMMDD').isValid()) {
                throw new Error('Invalid date2');
            }
            sql += ` AND fee.FEE_DATE BETWEEN 
            TO_DATE('${params.betweenDate.date1}','yymmdd') AND TO_DATE('${params.betweenDate.date2}','yymmdd')`;
        }
        if (params.beforeDate) {
            sql += ` AND fee.FEE_DATE <= TO_DATE('${moment(
                params.beforeDate,
            ).format('YYYY-MM-DD')}','YYYY-MM-DD') `;
        }

        try {
            let rows: any;

            switch (this.company) {
                case CLINICO:
                    rows = await wpcHaDb.raw(sql);
                    break;
                case SKD:
                    rows = await wpcSkdDb.raw(sql);
                    break;
                case IB:
                    rows = await wpcIbDb.raw(sql);
                    break;
            }

            for (const row of rows) {
                result.push({
                    period: row.PERIOD, // 期數
                    applicationId: row.APPLICATION_CODE, // 申請書編號
                    method: row.METHOD, // 付款方式 1:現金 2:信用卡
                    cardNumber: row.CCDID, // 卡號
                    personId: row.IDENTITYCARD, // 身分證字號
                    validThru: row.EXPIREDDATE, // 卡片到期日
                    amount: row.FEE, // 費用
                    orderId: row.SOCODE, // 訂單單號
                    startDate: row.STARTDATE, // 起始日
                    endDate: row.ENDDATE, // 到期日
                    isPaid: row.FEESTATE == 'Y', // 繳費狀態
                    company: row.LEGAL == 'HA' ? 'CLINICO' : row.LEGAL, // 公司編號
                    storeId: row.STOREID, // 門市編號
                    itemCode: row.ITEMCODE, // 料號
                    orderDate: row.FEE_DATE, //出貨日期
                    shipperId: row.SHIP_CODE, //出貨單號
                    active: row.ACTIVE,
                    SN: row.SN,
                    line: row.LINE,
                    askingId: row.ASKING_ID,
                    isACH: row.ACHSTATUS == 'Y', // 啟用銀行扣款(ACH) N: 未通過 Y:已通過 P:送件中
                    bankCode: row.BANKCODE,
                    bankBranchCode: row.BANKBRANCHCODE,
                    bankAccount: row.BANKACCOUNT,
                    bankIdentityCard: row.BANKIDENTITYCARD,
                    memberCode: row.MEMBERID,
                });
            }
            return result;
        } catch (err) {
            logger.error(`[${this.company}] 補刷訂單查詢失敗: ${err} `);
            return [];
        }
    }

    async create(params: ICreateRepayParams): Promise<void> {
        const sql = `INSERT INTO RS_FEE_REPAY (APPLICATION_CODE, SHIP_CODE, PERIOD, FEE, STATE, METHOD, ACTIVE, CREATE_DATE, CREATE_TIME, STORE_CODE, USER_CODE, LEGAL) 
            VALUES(
                '${params.applicationCode}',
                '${params.shipCode}',
                ${params.period},
                ${params.fee},
                'N',
                '${params.method}',
                'Y',
                TO_DATE('${moment().format('YYYY-MM-DD')}', 'YYYY-MM-DD'),
                '${moment().format('HH:mm:ss')}',
                '${params.storeCode}',
                '${params.userCode}',
                '${params.legal}'
                )`;

        try {
            switch (this.company) {
                case CLINICO:
                    await wpcHaDb.raw(sql);
                    break;
                case SKD:
                    await wpcSkdDb.raw(sql);
                    break;
                case IB:
                    await wpcIbDb.raw(sql);
                    break;
            }
        } catch (err) {
            logger.error(`[${this.company}] 補刷紀錄產生失敗: ${err} `);
            throw err;
        }
    }

    async updateAskingId(params: IUpdateRepayParams): Promise<void> {
        const updateRsFee = `update RS_FEE_REPAY
            set ASKING_ID = ${params.askingId}
            where APPLICATION_CODE = '${params.filters.applicationCode}' 
            and PERIOD = '${params.filters.period}'
            and STATE = 'N' 
            and ACTIVE = 'Y'
            `;

        try {
            switch (this.company) {
                case CLINICO:
                    await wpcHaDb.raw(updateRsFee);
                    break;
                case SKD:
                    await wpcSkdDb.raw(updateRsFee);
                    break;
                case IB:
                    await wpcIbDb.raw(updateRsFee);
                    break;
            }
            return;
        } catch (err) {
            logger.error(
                `[${this.company}] 寫入ASKING_ID(#${params.askingId})失敗: ${err} `,
            );
            throw err;
        }
    }

    async updateProcessStatus(
        params: IUpdateRepayProcessParams,
    ): Promise<void> {
        const updateRsFee = `update RS_FEE_REPAY
            set IS_PROCESS = 'Y'
            where APPLICATION_CODE = '${params.filters.applicationCode}' 
            and PERIOD = '${params.filters.period}'
            and STATE = 'N' 
            and ACTIVE = 'Y'
            `;

        try {
            switch (this.company) {
                case CLINICO:
                    await wpcHaDb.raw(updateRsFee);
                    break;
                case SKD:
                    await wpcSkdDb.raw(updateRsFee);
                    break;
                case IB:
                    await wpcIbDb.raw(updateRsFee);
                    break;
            }
            return;
        } catch (err) {
            logger.error(`[${this.company}] set procrss error: ${err} `);
            throw err;
        }
    }

    async updateStateSuccess(params: {
        applicationCode: string;
        period: number;
    }): Promise<void> {
        const updateRsFee = `update RS_FEE_REPAY
            set STATE = 'Y'
            where APPLICATION_CODE = '${params.applicationCode}' 
            and PERIOD = '${params.period}'
            and STATE = 'N' 
            and ACTIVE = 'Y'
            `;

        try {
            switch (this.company) {
                case CLINICO:
                    await wpcHaDb.raw(updateRsFee);
                    break;
                case SKD:
                    await wpcSkdDb.raw(updateRsFee);
                    break;
                case IB:
                    await wpcIbDb.raw(updateRsFee);
                    break;
            }
            return;
        } catch (err) {
            logger.error(
                `[${this.company}]UPDATE RS_FEE_REPAY STATE = 'Y' 失敗: ${err} `,
            );
            throw err;
        }
    }

    /** 狀態恢復讓補扣款失敗再次補扣 */
    async updateStateFailedForACH(params: {
        applicationCode: string;
        period: number;
    }): Promise<void> {
        const updateRsFee = `update RS_FEE_REPAY
            set STATE = 'N', IS_PROCESS = 'N' 
            where APPLICATION_CODE = '${params.applicationCode}' 
            and PERIOD = '${params.period}'
            and ACTIVE = 'Y'
            `;

        try {
            switch (this.company) {
                case CLINICO:
                    await wpcHaDb.raw(updateRsFee);
                    break;
                case SKD:
                    await wpcSkdDb.raw(updateRsFee);
                    break;
                case IB:
                    await wpcIbDb.raw(updateRsFee);
                    break;
            }
            return;
        } catch (err) {
            logger.error(
                `[${this.company}]UPDATE RS_FEE_REPAY STATE = 'Y' 失敗: ${err} `,
            );
            throw err;
        }
    }

    async updateFeeDate(params: {
        applicationCode: string;
        period: number;
        feeDate: Date;
        memo: string;
    }): Promise<void> {
        const updateRsFee = `update RS_FEE_REPAY
            set FEE_DATE = TO_DATE('${moment(params.feeDate).format(
                'YYYY-MM-DD',
            )}','YYYY-MM-DD'),
            MEMO = '${params.memo}'
            where APPLICATION_CODE = '${params.applicationCode}' 
            and PERIOD = '${params.period}'
            and STATE = 'N' 
            and ACTIVE = 'Y'
            `;

        try {
            switch (this.company) {
                case CLINICO:
                    await wpcHaDb.raw(updateRsFee);
                    break;
                case SKD:
                    await wpcSkdDb.raw(updateRsFee);
                    break;
                case IB:
                    await wpcIbDb.raw(updateRsFee);
                    break;
            }
            return;
        } catch (err) {
            logger.error(
                `[${this.company}]UPDATE RS_FEE_REPAY.FEE_DATE 失敗: ${err} `,
            );
            throw err;
        }
    }
}
