export interface EsunAuthorizeHeaderRecord {
    /** 檔案開頭，此欄位固定為"FH"(檢核項)
     * @Length 2 */
    FH: string;
    /** 特店代碼
     * @Length 15 */
    storeCode: string;
    /** 專案代碼
     *  @Length 4 */
    projectCode: string;
}

export interface EsunAuthorizeDataRecord {
    /** 檔案開頭，此欄位固定為"FD"(檢核項)
     * @Length 2 */
    FD: string;
    /** 交易碼
     * @Length 2
     * @description
     * 請款交易‘05’
     * 退貨交易‘06’
     * 非’05’及’06’交易碼會踢除該筆交易。
     */
    transactionCode: string;

    /** 特店代碼
     * @Length 15
     */
    machineId: string;

    /** 子特店代號
     * @Length 20
     * @description
     * 空白 */
    subMachineId: string;

    /** 終端機代號
     * @Length 8
     * @description
     * BT000001(一般交易) or BT000002(分期交易) */
    terminalId: string;

    /** 訂單編號
     * @Length 20
     * @description
     * 由特約商店產生，不可重複，不可包含【_】字元，且英數限用"大寫"。 */
    orderCode: string;

    /** 交易金額
     * @Length 12
     * @description
     * 1. 幣別為台幣
     * 2. 整數 10 碼 + 小數 2 碼，不足由左補滿 0 */
    amount: string;

    /** 押碼
     * @Length 32
     * @description
     * 補空白 */
    mac: string;

    /** 卡號
     * @Length 19
     */
    cardNumber: string;

    /** 有效年月
     * @Length 4
     * @description
     * YYMM */
    validThru: string;

    /** 驗證代碼(身分證字號)
     * @Length 11
     * @description
     * 特殊檢核資料，不足右補空白。 */
    personId: string;

    /** CVC2
     * @Length 3
     * @description
     * 特殊檢核資料 */
    securityCode: string;

    /** 註冊碼
     * @Length 4
     * @description
     * 空白 */
    regisCode: string;

    /** 抵用紅利點 1
     * @Length 8
     * @description
     * 補滿 0 */
    bonus1: string;

    /** 抵用紅利點 2
     * @Length 8
     * @description
     * 補滿 0 */
    bonus2: string;

    /** 抵用紅利點 3
     * @Length 8
     * @description
     * 補滿 0 */
    bonus3: string;

    /** 抵用紅利點 4
     * @Length 8
     * @description
     * 補滿 0 */
    bonus4: string;

    /** 抵用紅利點 5
     * @Length 8
     * @description
     * 補滿 0 */
    bonus5: string;

    /** 分期產品代號
     * @Length 10
     * @description
     * 不足右補空白。 */
    bonusCode: string;

    /** 銀行紅利交易
     * @Length 1
     * @description
     * ‘Y’使用銀行紅利交易 */
    isBonus: string;

    /** 回覆碼
     * @Length 2
     * @description
     * 空白 */
    respondCode: string;

    /** 錯誤代碼
     * @Length 2
     * @description
     * 空白 */
    errorCode: string;

    /** 授權碼
     * @Length 6
     * @description
     * 空白 */
    authorizationCode: string;

    /** 消費明細英文說明
     * @Length 25
     * @description
     * 1. 屬國外卡交易時，顧客帳單將顯示此欄位。
     * 2. 須為半型字25個字，長度不足者補半形空白。
     * ※兩欄位皆為空值時，顧客帳單將顯示「特店營業名稱」。 */
    descriptionEn: string;

    /** 消費明細中文說明
     * @Length 20
     * @description
     * 1. 屬國內卡交易時，顧客帳單將顯示此欄位。
     * 2. 中文全形字20個字，長度不足者補全形空白。 */
    description: string;

    /** 備註
     * @Length 20
     * @description
     * 依專案代碼使用，否則補半形空白 */
    remark: string;
}

export interface EsunAuthorizeTrailerRecord {
    /** 檔案開頭 ，此欄位固定為"FT"(檢核項)
     * @Length 2 */
    FT: string;
    /** 總筆數
     * @Length 6 */
    count: string;
}

export interface EsunAuthorizeRespondDataRecord
    extends EsunAuthorizeDataRecord {
    /** 交易成功之卡號
     * @Length 19
     */
    successCardNumber?: string;

    /** 交易成功之有效年月
     * @Length 4
     * @description
     * YYMM */
    successValidThru?: string;
}

export interface EsunRequireHeaderRecord {
    /** 檔案開頭，此欄位固定為"FH"(檢核項)
     * @Length 2 */
    FH: string;
    /** 特店代碼
     * @Length 15 */
    storeCode: string;
    /** 專案代碼
     *  @Length 4 */
    projectCode: string;
    /** 專案代碼
     *  @Length 1 */
    type: string;
    /** 專案代碼
     *  @Length 7 */
    space: string;
}
