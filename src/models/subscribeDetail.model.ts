export interface SubscribeDetailModel {
    rsId: string; // 訂閱申請書單號
    orderSeqId: number; // 訂單項次
    paymentPeriod: number; // 繳費期別
    estimatedCardDate: string; // 預計刷卡日
    estimatedShippingDate: string; // 預計出貨日
    paymentType?: string; // 繳款方式2:信用卡
    expectedCardAmount: number; // 預計刷卡金額
    orderId?: string; // 訂單單號
    shippingId?: string; // 出貨單號
    paymentRemark?: string; // 備註
    isProcessed: string; // 執行刷卡否
    isCompleted: string; // 成功否刷卡異常否?
    isReCard: string; // 補刷否
    createdBy?: string; // 資料建立者 `tiptop`
    createdGroup?: string; // 資料建立部門 `tiptop`
    createdDate?: string; // 建立日期
    createdTime?: string; // 建立時間
    storeId: string; // 所屬營運中心
    legal: string; // 所屬法人碼
    askingId?: number;
}
