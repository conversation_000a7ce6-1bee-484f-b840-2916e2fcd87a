import { Request, Response } from 'express';
import * as httpErrors from 'http-errors';

export function error<PERSON><PERSON><PERSON>(error, req: Request, res: Response, next) {
    if (error instanceof Error) {
        const result = new httpErrors.InternalServerError(error.message);
        res.json(result);
        res.end();
        return;
    } else {
        const result = new httpErrors.InternalServerError('unknown object');
        res.json(result);
        res.end();
        return;
    }
}
