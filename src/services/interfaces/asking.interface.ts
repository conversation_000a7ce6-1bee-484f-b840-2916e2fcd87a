import { ShipperType } from './order.interface';

export interface IPublishAskingParams {
    assignedId?: number;
    watcherIds?: number[];
    subject: string;
    descriptionInput:
        | IAskingDescriptionParams
        | IAskingACHDescriptionParams
        | IFoodAskingDescriptionParams
        | string;
    parentAskingId?: number;
}

export interface IAskingDescriptionParams {
    productType: string; //商品: HA or RS
    period: number; //訂閱期數: 26 期
    date: string; //刷卡日期: 2022-05-04
    orderCode: string; //訂單單號
    shipCode: string; //出貨單號: RS501-HA2203020001
    memberCode: string; //會員編號: HAxxxxxxxx
    memberName: string; //會員姓名: XXX
    storeName: string; //所屬門市: xxxx
    userName: string; //負責聽力師: xxxx
    cardNumber: string; //刷卡卡號: xxxxxxx-1234 (只顯示後 4 碼)
    errorMessage: string; //失敗原因: 卡片資料驗證失敗!
}

export interface IFoodAskingDescriptionParams {
    productType: string; //商品: HA or RS
    period: number; //訂閱期數: 26 期
    date: string; //刷卡日期: 2022-05-04
    orderId: string; //訂單單號
    memberCode: string; //會員編號: HAxxxxxxxx
    memberName: string; //會員姓名: XXX
    storeName: string; //所屬門市: xxxx
    userName: string; //負責聽力師: xxxx
    cardNumber: string; //刷卡卡號: xxxxxxx-1234 (只顯示後 4 碼)
    errorMessage: string; //失敗原因: 卡片資料驗證失敗!
}

export interface IAskingACHDescriptionParams {
    productType: string; //商品: HA or RS
    period: number; //訂閱期數: 26 期
    date: string; //扣款日期: 2022-05-04
    orderCode: string; //訂單單號
    shipCode: string; //出貨單號: RS501-HA2203020001
    memberCode: string; //會員編號: HAxxxxxxxx
    memberName: string; //會員姓名: XXX
    storeName: string; //所屬門市: xxxx
    userName: string; //負責聽力師: xxxx
    bankCode: string; //
    bankAccount: string; //
    errorMessage: string; //失敗原因: 卡片資料驗證失敗!
}

export interface IUpdateAskingParams {
    askingId: number;
    notes: string;
    isClosed?: boolean;
    invoiceCode?: string;
}

export interface IPayFailed {
    orderType: ShipperType;
    applicationId: string;
    period: number;
    errorMessage: string;
    storeCode: string;
}

export interface IFailedParams extends IPayFailed {
    memberCode: string;
    memberName: string;
    userCode: string;
    userName: string;
    storeName: string;
    shipCode: string;
    cardNumber: string;
}

export interface IFoodFailedParams extends IPayFailed {
    memberCode: string;
    memberName: string;
    userCode: string;
    userName: string;
    storeName: string;
    orderId: string;
    cardNumber: string;
}

export interface IACHFailedParams extends IPayFailed {
    memberCode: string;
    memberName: string;
    userCode: string;
    userName: string;
    storeName: string;
    shipCode: string;
    bankCode: string;
    bankAccount: string;
}

export interface IRepayCompletedParams {
    applicationId: string;
    period: number;
}

export interface IRepayFailedParams extends IRepayCompletedParams {
    errorMessage: string;
}

export interface IReplyParams extends IRepayCompletedParams {
    achDate: string;
}

export interface IInvoiceFailedParams {
    shipCode: string;
    errorMessage: string;
}

export interface IPayByAmericanExpress {
    orderType: ShipperType;
    applicationId: string;
    period: number;
    shipCode: string;
    isRepay: boolean;
}
