import { Router, Request, Response } from 'express';
import * as multer from 'multer';
import * as httpErrors from 'http-errors';
import { BankACHService } from '../services/bankACH.service';

const storage = multer.memoryStorage();
const upload = multer({ storage: storage });
const router = Router();

/**
 * @name 上傳ACH回覆檔
 * @path [POST] /api/ach/upload
 **/
router.post(
    '/upload',
    upload.single('file'),
    async (req: Request, res: Response, next) => {
        try {
            if (!req.file || Object.keys(req.file).length === 0) {
                throw new httpErrors.BadRequest('請選擇檔案');
            }
            if (!req.body.company) {
                throw new httpErrors.BadRequest('請選擇通路');
            }
            const companyCode = req.body.company;
            const bankACHService = new BankACHService(companyCode);
            const result = await bankACHService.generateResponse(
                req.file.buffer,
            );
            res.json({ success: true, result });
        } catch (err) {
            next(err);
        }
    },
);

export default router;
