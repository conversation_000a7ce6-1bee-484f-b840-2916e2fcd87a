{"arrowParens": "always", "bracketSpacing": true, "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "printWidth": 80, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": true, "tabWidth": 4, "trailingComma": "all", "useTabs": false, "vueIndentScriptAndStyle": false, "importOrder": ["module-alias/register", "reflect-metadata", "(.*)/preboot", "^@clinico/(.*)$", "<THIRD_PARTY_MODULES>", "@/config", "^@/(.*)$", "^[./]"], "importOrderParserPlugins": ["typescript", "classProperties", "decorators-legacy"], "overrides": [{"files": ["*.json", "*.yml", "*rc"], "options": {"tabWidth": 2}}]}