import knex from 'knex';

console.log(process.env.WPC_DBUSER1_DB_USER);
console.log(process.env.WPC_DB_NAME);
const oracledb = knex({
    client: 'oracledb',
    connection: {
        host: process.env.WPC_DB_HOST,
        port: 1521,
        user: process.env.WPC_DBUSER1_DB_USER,
        password: process.env.WPC_DBUSER1_DB_PASSWORD,
        database: process.env.WPC_DB_NAME,
    },
    pool: { min: 0, max: 10 },
});

export default oracledb;
