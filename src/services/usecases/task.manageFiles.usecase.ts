import { BaseUseCase } from './shared/base.usecase';
import { FileManagerHelper } from './shared/fileManager.helper';

/**
 * 檔案管理的業務用例
 * 負責各種檔案的備份和管理操作
 *
 * 重構後使用共用的 FileManagerHelper 來避免重複程式碼
 */
export class ManageFilesUseCase extends BaseUseCase {
    private fileManagerHelper: FileManagerHelper;

    constructor(company: string) {
        super(company);
        this.fileManagerHelper = new FileManagerHelper(company);
    }

    /**
     * 備份已下載的檔案
     * @param fileNames - 檔案名稱列表
     */
    async backupDownloadedFiles(fileNames: string[]): Promise<void> {
        return this.executeWithErrorHandling(
            () => this.fileManagerHelper.backupDownloadedFiles(fileNames),
            'backup downloaded files',
            { fileCount: fileNames.length },
        );
    }

    /**
     * 備份未上傳的檔案
     */
    async backupNotUploadFiles(): Promise<void> {
        return this.executeWithErrorHandling(
            () => this.fileManagerHelper.backupNotUploadFiles(),
            'backup not uploaded files',
        );
    }

    /**
     * 備份上傳檔案
     */
    async backupUploadFiles(): Promise<void> {
        return this.executeWithErrorHandling(
            () => this.fileManagerHelper.backupUploadFiles(),
            'backup upload files',
        );
    }

    /**
     * 備份 ACH 檔案
     */
    async backupACHFiles(): Promise<void> {
        return this.executeWithErrorHandling(
            () => this.fileManagerHelper.backupACHFiles(),
            'backup ACH files',
        );
    }

    /**
     * 取得檔案路徑列表
     * @param folderPath - 資料夾路徑
     * @returns 檔案路徑列表
     */
    getFilePaths(folderPath: string): string[] {
        return this.fileManagerHelper.getFilePaths(folderPath);
    }

    /**
     * 清理臨時檔案
     * @param olderThanDays - 清理多少天前的檔案
     */
    async cleanupTempFiles(olderThanDays = 7): Promise<void> {
        return this.executeWithErrorHandling(
            () => this.fileManagerHelper.cleanupTempFiles(olderThanDays),
            'cleanup temporary files',
            { olderThanDays },
        );
    }

    /**
     * 驗證檔案完整性
     * @param fileNames - 檔案名稱列表
     * @returns 驗證結果
     */
    async validateFileIntegrity(fileNames: string[]): Promise<{
        valid: string[];
        invalid: string[];
    }> {
        return this.executeWithErrorHandling(
            () => this.fileManagerHelper.validateFileIntegrity(fileNames),
            'validate file integrity',
            { fileCount: fileNames.length },
        );
    }
}
