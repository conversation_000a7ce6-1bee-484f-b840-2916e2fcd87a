import { EsunService } from './esun.service';
import logger from './logger.service';
import { TaskUseCaseFactory } from './usecases';

/**
 * 食品任務服務類別
 * 只包含主要的業務流程步驟，細節實作委託給各個 Food Use Cases
 */
export class FoodTaskService {
    private company: string;
    private shipDate?: Date;
    private esunService: EsunService;
    private useCases: TaskUseCaseFactory;

    constructor(company: string, shipDate?: Date) {
        this.company = company;
        this.shipDate = shipDate;
        this.esunService = new EsunService(this.company);
        this.useCases = new TaskUseCaseFactory(this.company);

        // 設定事件監聽器
        this.setupEventListeners();
    }

    /**
     * 設定玉山服務的事件監聽器
     */
    private setupEventListeners(): void {
        this.esunService.emitter.on('jar_upload_closed', async (data) => {
            logger.info(`[${this.company}] ${data.stdout}`);
            await this.useCases
                .getManageFoodFilesUseCase()
                .backupNotUploadFiles();
        });

        this.esunService.emitter.on('jar_download_closed', async (data) => {
            logger.info(`[${this.company}] ${data.stdout}`);
            await this.generateResponse({ isRepay: false });
        });
    }

    /**
     * 上傳授權
     * 如果沒帶cardDate 代表系統排程觸發，此時搜尋條件是用 下次出貨日 nextShippingDate
     * 有帶cardDate 代表手動指定刷卡日觸發，此時搜尋條件是用 預計刷卡日 estimatedShippingDate
     */
    async upload(cardDate?: Date): Promise<void> {
        logger.info(`[${this.company}] Start food generate request`);

        // 1. 搜尋待處理的食品訂單
        const orders = await this.useCases
            .getProcessFoodOrderUseCase()
            .searchPendingOrders(cardDate);

        // 2. 處理食品付款授權
        await this.useCases
            .getProcessFoodPaymentUseCase()
            .execute(orders, cardDate);

        logger.info(`[${this.company}] Food upload completed`);
    }

    /**
     * 下載玉山回應檔案
     */
    async download(): Promise<void> {
        logger.info(`[${this.company}] Start food generate response`);
        await this.useCases.getProcessFoodPaymentUseCase().downloadResponse();
        logger.info(`[${this.company}] Food response downloaded!`);
    }

    /**
     * 處理玉山回應檔案的主要流程
     * @param params - 包含是否為補刷的參數
     */
    async generateResponse(params: { isRepay: boolean }): Promise<void> {
        try {
            // 1. 分析玉山回應檔案
            const responseResult = await this.useCases
                .getProcessFoodPaymentUseCase()
                .analyzeResponse(this.shipDate, params.isRepay);
            if (!responseResult) {
                return;
            }

            // 2. 處理每筆授權記錄
            await this.processAuthorizeRecords(
                responseResult.authorizeDataRecord,
            );

            // 3. 備份已下載的檔案
            await this.useCases
                .getManageFoodFilesUseCase()
                .backupDownloadedFiles(responseResult.filesName);

            logger.info(
                `[${this.company}] Food response processing completed successfully`,
            );
        } catch (error) {
            logger.error(
                `[${this.company}] Error in food generateResponse: ${error.message}`,
            );
            throw error;
        }
    }

    /**
     * 處理授權記錄列表
     * @param authorizeRecords - 授權記錄列表
     */
    private async processAuthorizeRecords(
        authorizeRecords: any[],
    ): Promise<void> {
        for (const authorizeRecord of authorizeRecords) {
            await this.processAuthorizeRecord(authorizeRecord);
        }
    }

    /**
     * 處理單筆授權記錄
     * @param authorizeRecord - 授權記錄
     */
    private async processAuthorizeRecord(authorizeRecord: any): Promise<void> {
        try {
            // 1. 驗證訂單資料
            if (
                !this.useCases
                    .getProcessFoodOrderUseCase()
                    .validateOrderData(authorizeRecord)
            ) {
                return;
            }

            // 2. 取得訂閱明細
            const subscribeDetail = await this.useCases
                .getProcessFoodOrderUseCase()
                .getSubscribeDetail(authorizeRecord.orderId);

            // 3. 更新銀行交易狀態
            await this.useCases
                .getProcessFoodOrderUseCase()
                .updateSubscribeCardState(authorizeRecord.orderId, {
                    bankRespondCode: authorizeRecord.respondCode,
                });

            // 4. 處理付款結果
            const result = await this.useCases
                .getProcessFoodResponseUseCase()
                .processAuthorizeRecord(authorizeRecord, subscribeDetail);

            // 5. 如果付款成功，處理出貨
            if (result.isSuccess) {
                await this.processSuccessfulPayment(authorizeRecord);
            }
        } catch (error) {
            logger.error(
                `[${this.company}] Error processing authorize record ${authorizeRecord.orderId}: ${error.message}`,
            );
        }
    }

    /**
     * 處理付款成功的後續流程
     * @param authorizeRecord - 授權記錄
     */
    private async processSuccessfulPayment(
        authorizeRecord: any,
    ): Promise<void> {
        // 1. 建立出貨單
        const shipmentId = await this.useCases
            .getProcessFoodShipmentUseCase()
            .execute(authorizeRecord, this.shipDate);

        // 2. 更新訂單狀態
        if (shipmentId) {
            await this.useCases
                .getProcessFoodOrderUseCase()
                .updateSubscribeCardState(authorizeRecord.orderId, {
                    shippingId: shipmentId,
                    isCompleted: 'Y',
                });
        }
    }
}
