import { WpcService } from '../wpc.service';
import {
    BaseUseCase,
    EsunServiceHelper,
    FileManagerHelper,
    DataValidatorHelper,
} from './shared';

/**
 * 處理食品付款的業務用例
 * 負責食品訂單的付款授權處理
 *
 * 重構後使用共用工具來避免重複程式碼
 */
export class ProcessFoodPaymentUseCase extends BaseUseCase {
    private wpcService: WpcService;
    private esunServiceHelper: EsunServiceHelper;
    private fileManagerHelper: FileManagerHelper;
    private dataValidatorHelper: DataValidatorHelper;

    constructor(company: string) {
        super(company);
        this.wpcService = new WpcService(this.company);
        this.esunServiceHelper = new EsunServiceHelper(company);
        this.fileManagerHelper = new FileManagerHelper(company);
        this.dataValidatorHelper = new DataValidatorHelper(company);
    }

    /**
     * 處理食品訂單付款授權的完整流程
     * @param orders - 訂單列表
     * @param cardDate - 刷卡日期
     */
    async execute(orders: any[], cardDate?: Date): Promise<void> {
        return this.executeWithErrorHandling(
            async () => {
                // 檢查是否有訂單需要處理
                if (
                    this.dataValidatorHelper.isEmptyArray(orders, 'food orders')
                ) {
                    return;
                }

                this.logInfo(
                    `Processing payment for ${orders.length} food orders`,
                );

                // 1. 建立食品訂單
                const createShippersResult = await this.createFoodOrders(
                    orders,
                    cardDate,
                );

                // 2. 處理付款授權
                if (createShippersResult.length > 0) {
                    await this.processPaymentAuthorization(
                        createShippersResult,
                    );
                } else {
                    this.logInfo(
                        'No food orders created, skipping payment authorization',
                    );
                }

                this.logInfo('Food payment processing completed');
            },
            'process food payment',
            { orderCount: orders.length, cardDate },
        );
    }

    /**
     * 建立食品訂單
     * @param orders - 訂單列表
     * @param cardDate - 刷卡日期
     * @returns 建立的訂單結果
     */
    private async createFoodOrders(
        orders: any[],
        cardDate?: Date,
    ): Promise<any[]> {
        return this.executeWithErrorHandling(
            async () => {
                this.logInfo('Creating food orders via WPC API');

                const createShippersResult =
                    await this.wpcService.createFoodOrders(orders, cardDate);

                this.logInfo(
                    `Food orders created: ${createShippersResult.length}`,
                );

                return createShippersResult;
            },
            'create food orders',
            { orderCount: orders.length, cardDate },
        );
    }

    /**
     * 處理付款授權
     * @param subscribeDetails - 訂閱明細列表
     */
    private async processPaymentAuthorization(
        subscribeDetails: any[],
    ): Promise<void> {
        return this.executeWithErrorHandling(
            async () => {
                this.logInfo('Generating food payment authorization request');

                // 1. 產生授權請求檔案
                await this.esunServiceHelper.generateFoodAuthorizationRequest(
                    subscribeDetails,
                    false,
                );

                // 2. 備份上傳檔案
                await this.fileManagerHelper.backupUploadFiles();

                // 3. 執行上傳
                await this.esunServiceHelper.uploadToEsun();

                this.logInfo('Payment authorization processing completed');
            },
            'process payment authorization',
            { subscribeDetailsCount: subscribeDetails.length },
        );
    }

    /**
     * 下載玉山回應檔案
     */
    async downloadResponse(): Promise<void> {
        return this.executeWithErrorHandling(
            () => this.esunServiceHelper.downloadResponse(false),
            'download food response',
        );
    }

    /**
     * 分析玉山回應檔案
     * @param shipDate - 出貨日期
     * @param isRepay - 是否為補刷
     * @returns 分析結果
     */
    async analyzeResponse(shipDate?: Date, isRepay = false): Promise<any> {
        return this.executeWithErrorHandling(
            () => this.esunServiceHelper.analyzeFoodResponse(shipDate, isRepay),
            'analyze food response',
            { shipDate, isRepay },
        );
    }
}
