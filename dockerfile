FROM ***********:5000/node-with-oracle-client:14.18.1-alpine3.11-v2
# install openjdk11-jre-headless
RUN apk update && apk upgrade && apk add openjdk11-jre-headless
WORKDIR /usr/src/app
COPY . .
RUN npm set registry http://***********:8081/repository/npm/
RUN npm i -g typescript@4.5.5
RUN npm i
RUN npm run build
RUN ["chmod", "+x", "./docker-entrypoint.sh"]

EXPOSE 8080

ENTRYPOINT ["sh", "./docker-entrypoint.sh" ]

