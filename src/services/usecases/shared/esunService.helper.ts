import { EsunService } from '../../esun.service';
import { IGenerateAuthorizeResponseResult } from '../../interfaces/esun.interface';
import logger from '../../logger.service';

/**
 * 玉山服務輔助類別
 * 提供統一的玉山服務操作功能，避免在各個 Use Case 中重複實作
 *
 * 注意：這是一個純工具類別，不包含業務邏輯，符合 Clean Architecture 原則
 */
export class EsunServiceHelper {
    private readonly company: string;
    private readonly esunService: EsunService;

    constructor(company: string) {
        this.company = company;
        this.esunService = new EsunService(this.company);
    }

    /**
     * 產生一般授權請求檔案
     * @param orders - 訂單列表
     * @param isRepay - 是否為補刷卡
     * @returns 產生的資料
     */
    async generateAuthorizationRequest(
        orders: any[],
        isRepay: boolean,
    ): Promise<any> {
        try {
            const data = await this.esunService.generateRequest({
                orders,
                isRepay,
            });
            logger.info(
                `[${this.company}] Authorization request file created! (${data.length})`,
            );
            return data;
        } catch (error) {
            logger.error(
                `[${this.company}] Error generating authorization request: ${error.message}`,
            );
            throw error;
        }
    }

    /**
     * 產生食品授權請求檔案
     * @param subscribeDetails - 訂閱明細列表
     * @param isRepay - 是否為補刷卡
     * @returns 產生的資料
     */
    async generateFoodAuthorizationRequest(
        subscribeDetails: any[],
        isRepay: boolean,
    ): Promise<any> {
        try {
            const data = await this.esunService.generateFoodRequest({
                subscribeDetails,
                isRepay,
            });
            logger.info(
                `[${this.company}] Food authorization request file created! (${data.length})`,
            );
            return data;
        } catch (error) {
            logger.error(
                `[${this.company}] Error generating food authorization request: ${error.message}`,
            );
            throw error;
        }
    }

    /**
     * 上傳檔案到玉山
     */
    async uploadToEsun(): Promise<void> {
        try {
            await this.esunService.execUpload();
            logger.info(
                `[${this.company}] Files uploaded to Esun successfully`,
            );
        } catch (error) {
            logger.error(
                `[${this.company}] Error uploading files to Esun: ${error.message}`,
            );
            throw error;
        }
    }

    /**
     * 下載玉山回應檔案
     * @param isRepay - 是否為補刷卡
     */
    async downloadResponse(isRepay = false): Promise<void> {
        try {
            await this.esunService.execDownload({ isRepay });
            logger.info(
                `[${this.company}] Response downloaded from Esun successfully`,
            );
        } catch (error) {
            logger.error(
                `[${this.company}] Error downloading response from Esun: ${error.message}`,
            );
            throw error;
        }
    }

    /**
     * 處理一般回應檔案
     * @param shipDate - 出貨日期
     * @param isRepay - 是否為補刷卡
     * @returns 回應結果或 null（如果沒有檔案需要處理）
     */
    async processResponse(
        shipDate: Date | undefined,
        isRepay: boolean,
    ): Promise<IGenerateAuthorizeResponseResult | null> {
        try {
            logger.info(
                `[${this.company}] Processing Esun response (isRepay: ${isRepay})`,
            );

            const res = await this.esunService.generateResponse({
                shipDate,
                isRepay,
            });

            if (res.filesName.length === 0) {
                logger.warn(`[${this.company}] No response file to process`);
                return null;
            }

            logger.info(
                `[${this.company}] Esun response processed: ${res.filesName.length} files`,
            );
            return res;
        } catch (error) {
            logger.error(
                `[${this.company}] Error processing Esun response: ${error.message}`,
            );
            throw error;
        }
    }

    /**
     * 分析食品回應檔案
     * @param shipDate - 出貨日期
     * @param isRepay - 是否為補刷
     * @returns 分析結果
     */
    async analyzeFoodResponse(shipDate?: Date, isRepay = false): Promise<any> {
        try {
            logger.info(`[${this.company}] Analyzing food response file`);

            const result = await this.esunService.analyzeFile({
                shipDate,
                isRepay,
            });

            if (result.filesName.length === 0) {
                logger.warn(
                    `[${this.company}] No food response file to analyze`,
                );
                return null;
            }

            logger.info(
                `[${this.company}] Food response analysis completed: ${result.authorizeDataRecord.length} records`,
            );

            return result;
        } catch (error) {
            logger.error(
                `[${this.company}] Error analyzing food response: ${error.message}`,
            );
            throw error;
        }
    }

    /**
     * 檢查回應檔案是否存在
     * @param filesName - 檔案名稱列表
     * @returns 是否有檔案需要處理
     */
    hasResponseFiles(filesName: string[]): boolean {
        const hasFiles = filesName.length > 0;
        if (!hasFiles) {
            logger.warn(`[${this.company}] No response files found`);
        }
        return hasFiles;
    }

    /**
     * 取得 EsunService 實例（用於特殊情況）
     * @returns EsunService 實例
     */
    getEsunService(): EsunService {
        return this.esunService;
    }
}
