import { EsunAuthorizeRespondData } from '../interfaces/esun.interface';
import { UpdatePaymentResult } from '../interfaces/payment.interface';
import { OrderService } from '../order.service';
import { WpcService } from '../wpc.service';
import { BaseUseCase, DataValidatorHelper } from './shared';

/**
 * 建立出貨單的業務用例
 * 負責處理訂單到出貨單的轉換和記錄
 *
 * 重構後使用共用工具來避免重複程式碼
 */
export class CreateShipperUseCase extends BaseUseCase {
    private orderService: OrderService;
    private wpcService: WpcService;
    private dataValidatorHelper: DataValidatorHelper;

    constructor(company: string) {
        super(company);
        this.orderService = new OrderService(this.company);
        this.wpcService = new WpcService(this.company);
        this.dataValidatorHelper = new DataValidatorHelper(company);
    }

    /**
     * 建立出貨單並記錄到付款清單
     * @param orders - 訂單列表
     * @param shipDate - 出貨日期
     * @returns 建立結果統計
     */
    async execute(
        orders: any[],
        shipDate?: Date,
    ): Promise<{
        total: number;
        cc: number;
        cash: number;
        free: number;
        ACH: number;
    }> {
        return this.executeWithErrorHandling(
            async () => {
                // 檢查是否有訂單需要處理
                if (this.dataValidatorHelper.isEmptyArray(orders, 'orders')) {
                    return { total: 0, cc: 0, cash: 0, free: 0, ACH: 0 };
                }

                this.logInfo(
                    `Creating shippingResult for ${orders.length} orders`,
                );

                // 1. 建立出貨單
                const createShippersResult =
                    await this.wpcService.createShippers(orders, shipDate);

                // 2. 更新付款清單中的出貨單 ID
                const result =
                    await this.orderService.updateShipperIdToPaymentList(
                        createShippersResult,
                    );

                this.logInfo('Shippers created successfully', result);

                return result;
            },
            'create shippingResult',
            { orderCount: orders.length, shipDate },
        );
    }

    /**
     * 更新出貨單狀態
     * @param responseData - 玉山回應資料
     * @returns 更新後的出貨單列表
     */
    async updateStatus(
        responseData: EsunAuthorizeRespondData[],
    ): Promise<UpdatePaymentResult[]> {
        return this.executeWithErrorHandling(
            async () => {
                // 檢查是否有回應資料需要處理
                if (
                    this.dataValidatorHelper.isEmptyArray(
                        responseData,
                        'response data',
                    )
                ) {
                    return [];
                }

                this.logInfo(
                    `Updating ${responseData.length} shippingResult status`,
                );

                const shippingResult = await this.wpcService.updateShippers(
                    responseData,
                );

                this.logInfo(
                    `shippingResult status updated! (${shippingResult.length})`,
                );

                return shippingResult;
            },
            'update shippingResult status',
            { responseDataCount: responseData.length },
        );
    }
}
