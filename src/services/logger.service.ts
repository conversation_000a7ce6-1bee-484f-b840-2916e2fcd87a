import * as winston from 'winston';
import 'winston-daily-rotate-file';
import configs from '../configs';
import { Helpers } from '@clinico/clinico-node-framework';
import slack from './slack.service';

const winstonLogger = winston.createLogger({
    transports: [
        new winston.transports.Console(),
        new winston.transports.DailyRotateFile({
            dirname: configs.log.path,
            filename: `vpos-%DATE%.log`,
            zippedArchive: true,
            maxFiles: process.env.LOG_KEEP_DAYS || '120d', // 保留幾天
        }),
    ],
    format: winston.format.combine(winston.format.simple()),
});

class Logger {
    bindMessage(message: string): string {
        const datetime = Helpers.Date.formatDateTime(Helpers.Date.now());
        return `${datetime} msg: ${message}`;
    }

    info(message: string): void {
        // 優先記錄到檔案，確保日誌不會遺失
        winstonLogger.log('info', this.bindMessage(message));

        // 非同步發送 Slack 通知，不等待結果
        if (process.env.NODE_ENV === 'production') {
            slack.info(message).catch(() => {
                // 靜默處理 Slack 錯誤，避免影響主要流程
            });
        }
    }

    warn(message: string): void {
        // 優先記錄到檔案，確保日誌不會遺失
        winstonLogger.log('warn', this.bindMessage(message));

        // 非同步發送 Slack 通知，不等待結果
        if (process.env.NODE_ENV === 'production') {
            slack.warning(message).catch(() => {
                // 靜默處理 Slack 錯誤，避免影響主要流程
            });
        }
    }

    error(message: string): void {
        // 優先記錄到檔案，確保日誌不會遺失
        winstonLogger.log('error', this.bindMessage(message));

        // 非同步發送 Slack 通知，不等待結果
        if (process.env.NODE_ENV === 'production') {
            slack.error(message).catch(() => {
                // 靜默處理 Slack 錯誤，避免影響主要流程
            });
        }
    }

    debug(message: string, data?: any): void {
        console.log(this.bindMessage(message), data);
    }
}

const logger = new Logger();
export default logger;
