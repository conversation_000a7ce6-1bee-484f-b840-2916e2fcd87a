import { ShipperType } from './order.interface';

export interface PaymentParmas {
    method: string; //付款方式 1:現金 2:信用卡 3:銀行ACH
    cardNumber: string; //卡號
    validThru: string; // 有效日期 YYMM、以卡片上有效日期為準則
    personId?: string; // 身分證字號
    amount: number; //交易金額
    shipperId: string; //出貨單號
    applicationId: string; //申請書編號
    period: number; //期數
    storeId: string;
    isACH: boolean;
    bankCode: string; //銀行代碼
    bankBranchCode: string; //分行代碼
    bankAccount: string; //銀行帳號
    bankIdentityCard: string; //用戶身分證號
    memberCode: string;
}

export interface PaymentResult {
    period: number; //期數
    storeId: string; //門市編號
    orderId: string; //訂單單號
    amount: number; //交易金額
    cardNumber: string; //卡號
    applicationId: string; //申請書編號
    itemCode: string; //料號
    isSuccess: boolean; //交易結果
    respondCode: string; //交易代碼
    respondMessage: string; //交易訊息
    authorizationCode: string; //交易授權碼
}

export interface CreateShipperParams {
    storeId: string; //門市編號
    orderId: string; //訂單單號
    itemCode: string; //料號
    period: number; // 期數
    line: number; //品項流水序號
    items: {
        line: number; //品項流水序號
        materialCode: string; //料號
    }[];
}

export interface CreateShipperResult {
    method: string; //付款方式 1:現金 2:信用卡
    shipperId: string; //出貨單單號
    orderId: string; //訂單單號
    applicationId: string; //申請書編號
    period: number; // 期數
    amount: number;
}

export interface UpdatePaymentParams {
    shipperId: string;
    /** 信用卡號或銀行帳號 */
    cardNumber: string;
    respondCode: string;
    authorizationCode: string;
    applicationId: string;
    period: number;
}

export interface UpdatePaymentResult {
    applicationId: string;
    period: number;
    shipperId: string;
    respondCode: string;
    respondMessage: string;
    errorMessage: string;
    orderType: ShipperType;
    storeId: string;
    amount: number;
    /**
     * 1: 現金
     * 2: 信用卡
     * 3: 銀行扣款
     */
    method: string; 
}

export interface PaymentFailed {
    shipperId: string;
    respondCode: string;
    respondMessage: string;
}

export interface VposRespondData extends UpdatePaymentParams {
    respondMessage: string;
    orderType: ShipperType;
    storeId: string;
    amount: number;
}

export interface ACHRespondData {
    shipperId: string;
    bankAccount: string;
    respondCode: string;
    authorizationCode: string;
    applicationId: string;
    period: number;
    respondMessage: string;
    orderType: ShipperType;
    storeId: string;
    amount: number;
}
