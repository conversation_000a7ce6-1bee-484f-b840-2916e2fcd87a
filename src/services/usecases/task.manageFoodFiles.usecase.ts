import { BaseUseCase } from './shared/base.usecase';
import { FileManagerHelper } from './shared/fileManager.helper';

/**
 * 食品檔案管理的業務用例
 * 負責食品相關檔案的備份和管理操作
 *
 * 重構後使用共用的 FileManagerHelper 來避免重複程式碼
 */
export class ManageFoodFilesUseCase extends BaseUseCase {
    private fileManagerHelper: FileManagerHelper;

    constructor(company: string) {
        super(company);
        this.fileManagerHelper = new FileManagerHelper(company);
    }

    /**
     * 備份已下載的食品回應檔案
     * @param fileNames - 檔案名稱列表
     */
    async backupDownloadedFiles(fileNames: string[]): Promise<void> {
        return this.executeWithErrorHandling(
            () => this.fileManagerHelper.backupDownloadedFiles(fileNames),
            'backup downloaded food files',
            { fileCount: fileNames.length },
        );
    }

    /**
     * 備份未上傳的食品檔案
     */
    async backupNotUploadFiles(): Promise<void> {
        return this.executeWithErrorHandling(
            () => this.fileManagerHelper.backupNotUploadFiles(),
            'backup not uploaded food files',
        );
    }

    /**
     * 備份上傳的食品檔案
     */
    async backupUploadFiles(): Promise<void> {
        return this.executeWithErrorHandling(
            () => this.fileManagerHelper.backupUploadFiles(),
            'backup uploaded food files',
        );
    }

    /**
     * 清理食品臨時檔案
     * @param olderThanDays - 清理多少天前的檔案
     */
    async cleanupTempFiles(olderThanDays = 7): Promise<void> {
        return this.executeWithErrorHandling(
            () => this.fileManagerHelper.cleanupTempFiles(olderThanDays),
            'cleanup food temporary files',
            { olderThanDays },
        );
    }

    /**
     * 取得食品檔案資訊
     * @param folderPath - 資料夾路徑
     * @returns 檔案資訊列表
     */
    getFoodFileInfo(folderPath: string): {
        totalFiles: number;
        filePaths: string[];
    } {
        const filePaths = this.fileManagerHelper.getFilePaths(folderPath);
        return {
            totalFiles: filePaths.length,
            filePaths: filePaths,
        };
    }

    /**
     * 驗證食品檔案完整性
     * @param fileNames - 檔案名稱列表
     * @returns 驗證結果
     */
    async validateFileIntegrity(fileNames: string[]): Promise<{
        valid: string[];
        invalid: string[];
    }> {
        return this.executeWithErrorHandling(
            () => this.fileManagerHelper.validateFileIntegrity(fileNames),
            'validate food file integrity',
            { fileCount: fileNames.length },
        );
    }
}
