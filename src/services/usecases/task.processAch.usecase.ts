import { HolidayService } from '../holiday.service';
import { MailService } from '../mail.service';
import configs from '../../configs';
import * as moment from 'moment';
import { BaseUseCase, FileManagerHelper, DataValidatorHelper } from './shared';

/**
 * 處理 ACH 相關業務的用例
 * 負責 ACH 檔案處理、郵件發送和工作日檢查
 *
 * 重構後使用共用工具來避免重複程式碼
 */
export class ProcessACHUseCase extends BaseUseCase {
    private holidayService: HolidayService;
    private mailService: MailService;
    private fileManagerHelper: FileManagerHelper;
    private dataValidatorHelper: DataValidatorHelper;

    constructor(company: string) {
        super(company);
        this.holidayService = new HolidayService();
        this.mailService = new MailService(this.company);
        this.fileManagerHelper = new FileManagerHelper(company);
        this.dataValidatorHelper = new DataValidatorHelper(company);
    }

    /**
     * 檢查是否為每週第一個工作天或倒數第二個工作天
     * @param date - 檢查日期
     * @returns 是否應該執行補扣款 ACH
     */
    async checkBusinessDaysForRepayACH(date: Date): Promise<boolean> {
        return this.executeWithErrorHandling(
            async () => {
                // 驗證日期
                if (
                    !this.dataValidatorHelper.validateDate(date, 'check date')
                ) {
                    return false;
                }

                this.logInfo(
                    `Checking business days for repay ACH on ${moment(
                        date,
                    ).format('YYYY-MM-DD')}`,
                );

                const businessDays =
                    await this.holidayService.weekBusinessDays();

                let day1: Date | null = null;
                if (businessDays.days > 0) {
                    day1 = await this.holidayService.weekBusinessDay(1);
                }

                let day2: Date | null = null;
                if (businessDays.days - 1 > 0) {
                    day2 = await this.holidayService.weekBusinessDay(
                        businessDays.days - 1,
                    );
                }

                const isFirstDay =
                    day1 !== null &&
                    moment(day1).isSame(moment(date).startOf('day'));
                const isSecondLastDay =
                    day2 !== null &&
                    moment(day2).isSame(moment(date).startOf('day'));

                const shouldProcess = isFirstDay || isSecondLastDay;

                this.logInfo('Business day check result', {
                    shouldProcess,
                    isFirstDay,
                    isSecondLastDay,
                    totalBusinessDays: businessDays.days,
                });

                return shouldProcess;
            },
            'check business days for repay ACH',
            { date: moment(date).format('YYYY-MM-DD') },
        );
    }

    /**
     * 發送 ACH 郵件並備份檔案
     * @param ACHDate - ACH 日期
     * @param repayACHDate - 補扣款 ACH 日期
     */
    async sendMailAndBackupFiles(
        ACHDate: Date | undefined,
        repayACHDate: Date | undefined,
    ): Promise<void> {
        return this.executeWithErrorHandling(
            async () => {
                this.logInfo('Processing ACH mail and backup');

                // 1. 取得檔案路徑
                const filePaths = this.getACHFilePaths();

                // 2. 發送郵件（如果有檔案）
                if (filePaths.length > 0) {
                    await this.sendACHMail(filePaths, ACHDate, repayACHDate);
                } else {
                    this.logInfo('No ACH files to send');
                }

                // 3. 備份檔案
                await this.fileManagerHelper.backupACHFiles();

                this.logInfo('ACH mail and backup processing completed');
            },
            'send ACH mail and backup files',
            { ACHDate, repayACHDate },
        );
    }

    /**
     * 取得 ACH 檔案路徑
     * @returns 檔案路徑列表
     */
    private getACHFilePaths(): string[] {
        const folderPath = `${configs.bank.exportACHFolder}/${this.company}`;
        const filePaths = this.fileManagerHelper.getFilePaths(folderPath);

        this.logInfo(`Found ${filePaths.length} ACH files in ${folderPath}`);

        return filePaths;
    }

    /**
     * 發送 ACH 郵件
     * @param filePaths - 檔案路徑列表
     * @param ACHDate - ACH 日期
     * @param repayACHDate - 補扣款 ACH 日期
     */
    private async sendACHMail(
        filePaths: string[],
        ACHDate: Date | undefined,
        repayACHDate: Date | undefined,
    ): Promise<void> {
        await this.mailService.sendACHMail({
            filePaths,
            ACHDate,
            repayACHDate,
        });

        this.logInfo(`ACH mail sent with ${filePaths.length} files`);
    }

    /**
     * 檢查是否為營業日
     * @param date - 檢查日期
     * @returns 是否為營業日
     */
    async checkBusinessDay(date: Date): Promise<boolean> {
        return this.executeWithErrorHandling(
            async () => {
                // 驗證日期
                if (
                    !this.dataValidatorHelper.validateDate(
                        date,
                        'business day check date',
                    )
                ) {
                    return false;
                }

                const isBusinessDay =
                    await this.holidayService.checkBusinessDay(date);
                this.logInfo(
                    `Business day check for ${moment(date).format(
                        'YYYY-MM-DD',
                    )}: ${isBusinessDay}`,
                );
                return isBusinessDay;
            },
            'check business day',
            { date: moment(date).format('YYYY-MM-DD') },
        );
    }
}
