import * as dotenvFlow from 'dotenv-flow';
const dotenv = dotenvFlow.config();

import { Utils } from '@clinico/clinico-node-framework';
import configs from './configs';
import * as cron from 'node-schedule';
import downloadJob from './schedules/download.job';
import uploadJob from './schedules/upload.job';
import { CLINICO, SKD, IB } from './const';
import logger from './services/logger.service';

Utils.Mailer.initialize(configs.smtp);
Utils.Redmine.initialize(configs.redmine);
Utils.SMS.initialize({
    ...configs.sms,
    account: Utils.SMS.Accounts.HA, // Default account
});

//CLINICO
cron.scheduleJob(process.env.HA_UPLOAD_TIME!, async () => {
    const job = new uploadJob(CLINICO);
    await job.run();
});
cron.scheduleJob(process.env.HA_DOWNLOAD_TIME!, async () => {
    const job = new downloadJob(CLINICO);
    await job.run();
});

//SKD
cron.scheduleJob(process.env.SKD_UPLOAD_TIME!, async () => {
    const job = new uploadJob(SKD);
    await job.run();
});
cron.scheduleJob(process.env.SKD_DOWNLOAD_TIME!, async () => {
    const job = new downloadJob(SKD);
    await job.run();
});

//IB
cron.scheduleJob(process.env.IB_UPLOAD_TIME!, async () => {
    const job = new uploadJob(IB);
    await job.run();
});
cron.scheduleJob(process.env.IB_DOWNLOAD_TIME!, async () => {
    const job = new downloadJob(IB);
    await job.run();
});

//CLINICO Repay
cron.scheduleJob(process.env.HA_REPAY_UPLOAD_TIME!, async () => {
    const job = new uploadJob(CLINICO);
    await job.repayRun();
});
cron.scheduleJob(process.env.HA_REPAY_DOWNLOAD_TIME!, async () => {
    const job = new downloadJob(CLINICO);
    await job.repayRun();
});

//SKD Repay
cron.scheduleJob(process.env.SKD_REPAY_UPLOAD_TIME!, async () => {
    const job = new uploadJob(SKD);
    await job.repayRun();
});
cron.scheduleJob(process.env.SKD_REPAY_DOWNLOAD_TIME!, async () => {
    const job = new downloadJob(SKD);
    await job.repayRun();
});

//IB Repay
cron.scheduleJob(process.env.IB_REPAY_UPLOAD_TIME!, async () => {
    const job = new uploadJob(IB);
    await job.repayRun();
});
cron.scheduleJob(process.env.IB_REPAY_DOWNLOAD_TIME!, async () => {
    const job = new downloadJob(IB);
    await job.repayRun();
});

// 食品訂閱制
cron.scheduleJob(process.env.HA_FOOD_UPLOAD_TIME!, async () => {
    const job = new uploadJob(CLINICO);
    await job.foodRun();
});

cron.scheduleJob(process.env.SKD_FOOD_UPLOAD_TIME!, async () => {
    const job = new uploadJob(SKD);
    await job.foodRun();
});

cron.scheduleJob(process.env.IB_FOOD_UPLOAD_TIME!, async () => {
    const job = new uploadJob(IB);
    await job.foodRun();
});

cron.scheduleJob(process.env.HA_FOOD_DOWNLOAD_TIME!, async () => {
    const job = new downloadJob(CLINICO);
    await job.foodRun();
});

cron.scheduleJob(process.env.SKD_FOOD_DOWNLOAD_TIME!, async () => {
    const job = new downloadJob(SKD);
    await job.foodRun();
});

cron.scheduleJob(process.env.IB_FOOD_DOWNLOAD_TIME!, async () => {
    const job = new downloadJob(IB);
    await job.foodRun();
});

console.log(`Job running!! (${process.env.NODE_ENV})`);
logger.info(`[DEBUG] configs.sms.enableSending: ${configs.sms?.enableSending}`);

const handleUncaughtExceptionOrRejection = (err) => {
    console.log('Uncaughted Exception or Unhandled Rejection happens!');
    logger.error(err);
};

process.on('unhandledRejection', handleUncaughtExceptionOrRejection);
process.on('uncaughtException', handleUncaughtExceptionOrRejection);
