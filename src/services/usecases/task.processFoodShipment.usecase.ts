import { WpcService } from '../wpc.service';
import { IWPCShipmentParams } from '../interfaces/order.interface';
import * as moment from 'moment';
import { BaseUseCase, DataValidatorHelper } from './shared';

/**
 * 處理食品出貨的業務用例
 * 負責建立食品出貨單和更新出貨狀態
 *
 * 重構後使用共用工具來避免重複程式碼
 */
export class ProcessFoodShipmentUseCase extends BaseUseCase {
    private wpcService: WpcService;
    private dataValidatorHelper: DataValidatorHelper;

    constructor(company: string) {
        super(company);
        this.wpcService = new WpcService(this.company);
        this.dataValidatorHelper = new DataValidatorHelper(company);
    }

    /**
     * 處理食品出貨的完整流程
     * @param authorizeRecord - 授權記錄
     * @param shipDate - 出貨日期
     * @returns 出貨單ID
     */
    async execute(
        authorizeRecord: any,
        shipDate?: Date,
    ): Promise<string | null> {
        return this.executeWithErrorHandling(
            async () => {
                // 驗證出貨資料
                if (!this.validateShipmentData(authorizeRecord)) {
                    return null;
                }

                this.logInfo(
                    `Processing food shipment for order: ${authorizeRecord.orderId}`,
                );

                // 1. 準備出貨參數
                const shipmentParams = this.prepareShipmentParams(
                    authorizeRecord,
                    shipDate,
                );

                // 2. 建立出貨單
                const shipmentId = await this.createShipment(
                    shipmentParams,
                    shipDate,
                );

                if (!shipmentId) {
                    this.logError(
                        `Failed to create food shipment for RS ID: ${authorizeRecord.rsId}`,
                    );
                    return null;
                }

                this.logInfo(
                    `Food shipment created successfully: ${shipmentId} for order: ${authorizeRecord.orderId}`,
                );

                return shipmentId;
            },
            'process food shipment',
            { orderId: authorizeRecord.orderId, rsId: authorizeRecord.rsId },
        );
    }

    /**
     * 準備出貨參數
     * @param authorizeRecord - 授權記錄
     * @param shipDate - 出貨日期
     * @returns 出貨參數
     */
    private prepareShipmentParams(
        authorizeRecord: any,
        shipDate?: Date,
    ): IWPCShipmentParams {
        const shipmentParams: IWPCShipmentParams = {
            storeId: authorizeRecord.storeId,
            date: moment(shipDate).toDate(),
            orderId: authorizeRecord.orderId,
            rsId: authorizeRecord.rsId,
        };

        this.logInfo('Shipment params prepared', shipmentParams);

        return shipmentParams;
    }

    /**
     * 建立食品出貨單
     * @param shipmentParams - 出貨參數
     * @param shipDate - 出貨日期
     * @returns 出貨單ID
     */
    private async createShipment(
        shipmentParams: IWPCShipmentParams,
        shipDate?: Date,
    ): Promise<string | null> {
        const shipmentId = await this.wpcService.createFoodShipment(
            shipmentParams,
            shipDate,
        );

        if (shipmentId) {
            this.logInfo(`Food shipment created with ID: ${shipmentId}`);
        } else {
            this.logWarn('Food shipment creation returned null');
        }

        return shipmentId;
    }

    /**
     * 驗證出貨參數
     * @param authorizeRecord - 授權記錄
     * @returns 是否有效
     */
    validateShipmentData(authorizeRecord: any): boolean {
        return this.dataValidatorHelper.validateShipmentData(authorizeRecord);
    }

    /**
     * 檢查是否可以建立出貨單
     * @param authorizeRecord - 授權記錄
     * @returns 是否可以出貨
     */
    canCreateShipment(authorizeRecord: any): boolean {
        // 只有付款成功的訂單才能建立出貨單
        const isPaymentSuccessful =
            this.dataValidatorHelper.isPaymentSuccessful(
                authorizeRecord.respondCode,
            );
        const hasValidData = this.validateShipmentData(authorizeRecord);

        return isPaymentSuccessful && hasValidData;
    }

    /**
     * 批次處理多個出貨單
     * @param authorizeRecords - 授權記錄列表
     * @param shipDate - 出貨日期
     * @returns 出貨結果統計
     */
    async processBatchShipments(
        authorizeRecords: any[],
        shipDate?: Date,
    ): Promise<{
        total: number;
        successful: number;
        failed: number;
        shipmentIds: string[];
    }> {
        return this.executeWithErrorHandling(
            async () => {
                // 檢查是否有記錄需要處理
                if (
                    this.dataValidatorHelper.isEmptyArray(
                        authorizeRecords,
                        'authorize records',
                    )
                ) {
                    return {
                        total: 0,
                        successful: 0,
                        failed: 0,
                        shipmentIds: [],
                    };
                }

                const result = {
                    total: authorizeRecords.length,
                    successful: 0,
                    failed: 0,
                    shipmentIds: [] as string[],
                };

                this.logInfo(
                    `Processing batch food shipments: ${result.total} records`,
                );

                for (const record of authorizeRecords) {
                    if (!this.canCreateShipment(record)) {
                        result.failed++;
                        continue;
                    }

                    const shipmentId = await this.safeExecute(
                        () => this.execute(record, shipDate),
                        `batch shipment for order ${record.orderId}`,
                    );

                    if (shipmentId) {
                        result.successful++;
                        result.shipmentIds.push(shipmentId);
                    } else {
                        result.failed++;
                    }
                }

                this.logInfo(
                    `Batch food shipments completed: ${result.successful} successful, ${result.failed} failed`,
                );

                return result;
            },
            'process batch food shipments',
            { recordCount: authorizeRecords.length },
        );
    }
}
