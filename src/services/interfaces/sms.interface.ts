import { ShipperType } from './order.interface';

export enum EnumSMSLogType {
    VPOS = 'vpos',
}

export interface SendSMSParams {
    subject: string;
    content: string;
    mobile: string;
    type?: EnumSMSLogType;
    referenceCode?: string;
}

export interface IPayFailed {
    orderType: ShipperType;
    applicationId: string;
    period: number;
    errorCode: string;
    errorMessage: string;
    storeCode: string;
    amount: number;
    method: string;
}
