import db from '@clinico/clinico-persistence';
import { Helpers } from '@clinico/clinico-node-framework';
import * as moment from 'moment';

export class HolidayService {
    constructor() {
        Promise.all([this.init()]);
    }

    async init() {
        const holidays = await db.Holiday.findAndCountAll({
            distinct: true,
            where: {
                regionId: 1,
            },
            order: [['date', 'ASC']],
        });
        const makeupWorkdays = await db.MakeupWorkday.findAndCountAll({
            distinct: true,
            where: {
                regionId: 1,
            },
            order: [['date', 'ASC']],
        });
        Helpers.Date.setConfig({
            holidays: holidays.rows.map((data) => data.date),
            makeupDays: makeupWorkdays.rows.map((data) => data.date),
        });
    }

    async checkBusinessDay(date: Date): Promise<boolean> {
        return Helpers.Date.isBusinessDay(moment(date));
    }

    async beforeBusinessDay(date: Date): Promise<Date> {
        return Helpers.Date.addBusinessDays(date, -1).toDate();
    }

    async afterBusinessDay(date: Date, day = 1): Promise<Date> {
        return Helpers.Date.addBusinessDays(date, day).toDate();
    }

    async weekBusinessDay(day = 1): Promise<Date | null> {
        const businessDay = Helpers.Date.weekBusinessDay(day);
        if (businessDay) {
            return businessDay.toDate();
        }
        return null;
    }

    async weekBusinessDays(): Promise<{ dates: Date[]; days: number }> {
        const dates = Helpers.Date.weekBusinessDays().map((day) =>
            day.toDate(),
        );
        return {
            dates,
            days: dates.length,
        };
    }
}
