/**
 * Task Use Cases 統一匯出
 *
 * 每個 use case 代表一個明確的業務操作：
 *
 * 一般訂閱業務：
 * - CreateShipperUseCase: 建立和管理出貨單
 * - ProcessCardPaymentUseCase: 處理信用卡付款流程
 * - ProcessRepayUseCase: 處理補刷卡流程
 * - ProcessFailedPaymentUseCase: 處理失敗付款的後續作業
 * - ProcessACHUseCase: 處理 ACH 相關業務
 * - ManageFilesUseCase: 檔案管理和備份
 *
 * 食品訂閱業務：
 * - ProcessFoodOrderUseCase: 處理食品訂單搜尋和建立
 * - ProcessFoodPaymentUseCase: 處理食品付款授權
 * - ProcessFoodResponseUseCase: 處理食品付款回應
 * - ProcessFoodShipmentUseCase: 處理食品出貨
 * - ManageFoodFilesUseCase: 食品檔案管理
 */

// 一般訂閱業務 Use Cases
export { CreateShipperUseCase } from './task.createShipper.usecase';
export { ProcessCardPaymentUseCase } from './task.processCardPayment.usecase';
export { ProcessRepayUseCase } from './task.processRepay.usecase';
export { ProcessFailedPaymentUseCase } from './task.processFailedPayment.usecase';
export { ProcessACHUseCase } from './task.processAch.usecase';
export { ManageFilesUseCase } from './task.manageFiles.usecase';

// 食品訂閱業務 Use Cases
export { ProcessFoodOrderUseCase } from './task.processFoodOrder.usecase';
export { ProcessFoodPaymentUseCase } from './task.processFoodPayment.usecase';
export { ProcessFoodResponseUseCase } from './task.processFoodResponse.usecase';
export { ProcessFoodShipmentUseCase } from './task.processFoodShipment.usecase';
export { ManageFoodFilesUseCase } from './task.manageFoodFiles.usecase';

// 匯入所有 Use Cases 用於工廠類別
import { CreateShipperUseCase } from './task.createShipper.usecase';
import { ProcessCardPaymentUseCase } from './task.processCardPayment.usecase';
import { ProcessRepayUseCase } from './task.processRepay.usecase';
import { ProcessFailedPaymentUseCase } from './task.processFailedPayment.usecase';
import { ProcessACHUseCase } from './task.processAch.usecase';
import { ManageFilesUseCase } from './task.manageFiles.usecase';
import { ProcessFoodOrderUseCase } from './task.processFoodOrder.usecase';
import { ProcessFoodPaymentUseCase } from './task.processFoodPayment.usecase';
import { ProcessFoodResponseUseCase } from './task.processFoodResponse.usecase';
import { ProcessFoodShipmentUseCase } from './task.processFoodShipment.usecase';
import { ManageFoodFilesUseCase } from './task.manageFoodFiles.usecase';

/**
 * Task Use Cases 工廠類別
 * 提供統一的方式來創建和管理所有 use cases
 */
export class TaskUseCaseFactory {
    private company: string;
    private useCases: Map<string, any> = new Map();

    constructor(company: string) {
        this.company = company;
        this.initializeUseCases();
    }

    /**
     * 初始化所有 use cases
     */
    private initializeUseCases(): void {
        // 一般訂閱業務 Use Cases
        this.useCases.set(
            'createShipper',
            new CreateShipperUseCase(this.company),
        );
        this.useCases.set(
            'processCardPayment',
            new ProcessCardPaymentUseCase(this.company),
        );
        this.useCases.set(
            'processRepay',
            new ProcessRepayUseCase(this.company),
        );
        this.useCases.set(
            'processFailedPayment',
            new ProcessFailedPaymentUseCase(this.company),
        );
        this.useCases.set('processACH', new ProcessACHUseCase(this.company));
        this.useCases.set('manageFiles', new ManageFilesUseCase(this.company));

        // 食品訂閱業務 Use Cases
        this.useCases.set(
            'processFoodOrder',
            new ProcessFoodOrderUseCase(this.company),
        );
        this.useCases.set(
            'processFoodPayment',
            new ProcessFoodPaymentUseCase(this.company),
        );
        this.useCases.set(
            'processFoodResponse',
            new ProcessFoodResponseUseCase(this.company),
        );
        this.useCases.set(
            'processFoodShipment',
            new ProcessFoodShipmentUseCase(this.company),
        );
        this.useCases.set(
            'manageFoodFiles',
            new ManageFoodFilesUseCase(this.company),
        );
    }

    /**
     * 取得建立出貨單 use case
     */
    getCreateShipperUseCase(): CreateShipperUseCase {
        return this.useCases.get('createShipper');
    }

    /**
     * 取得處理信用卡付款 use case
     */
    getProcessCardPaymentUseCase(): ProcessCardPaymentUseCase {
        return this.useCases.get('processCardPayment');
    }

    /**
     * 取得處理補刷卡 use case
     */
    getProcessRepayUseCase(): ProcessRepayUseCase {
        return this.useCases.get('processRepay');
    }

    /**
     * 取得處理失敗付款 use case
     */
    getProcessFailedPaymentUseCase(): ProcessFailedPaymentUseCase {
        return this.useCases.get('processFailedPayment');
    }

    /**
     * 取得處理 ACH use case
     */
    getProcessACHUseCase(): ProcessACHUseCase {
        return this.useCases.get('processACH');
    }

    /**
     * 取得檔案管理 use case
     */
    getManageFilesUseCase(): ManageFilesUseCase {
        return this.useCases.get('manageFiles');
    }

    // === 食品訂閱業務 Use Cases ===

    /**
     * 取得處理食品訂單 use case
     */
    getProcessFoodOrderUseCase(): ProcessFoodOrderUseCase {
        return this.useCases.get('processFoodOrder');
    }

    /**
     * 取得處理食品付款 use case
     */
    getProcessFoodPaymentUseCase(): ProcessFoodPaymentUseCase {
        return this.useCases.get('processFoodPayment');
    }

    /**
     * 取得處理食品回應 use case
     */
    getProcessFoodResponseUseCase(): ProcessFoodResponseUseCase {
        return this.useCases.get('processFoodResponse');
    }

    /**
     * 取得處理食品出貨 use case
     */
    getProcessFoodShipmentUseCase(): ProcessFoodShipmentUseCase {
        return this.useCases.get('processFoodShipment');
    }

    /**
     * 取得食品檔案管理 use case
     */
    getManageFoodFilesUseCase(): ManageFoodFilesUseCase {
        return this.useCases.get('manageFoodFiles');
    }

    /**
     * 取得所有 use cases
     */
    getAllUseCases(): Map<string, any> {
        return new Map(this.useCases);
    }
}
