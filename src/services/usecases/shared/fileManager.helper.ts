import fileUtil from '../../../utils/file';
import logger from '../../logger.service';

/**
 * 檔案管理輔助類別
 * 提供統一的檔案操作功能，避免在各個 Use Case 中重複實作
 *
 * 注意：這是一個純工具類別，不包含業務邏輯，符合 Clean Architecture 原則
 */
export class FileManagerHelper {
    private readonly company: string;

    constructor(company: string) {
        this.company = company;
    }

    /**
     * 備份已下載的檔案
     * @param fileNames - 檔案名稱列表
     */
    async backupDownloadedFiles(fileNames: string[]): Promise<void> {
        if (fileNames.length === 0) {
            logger.info(`[${this.company}] No downloaded files to backup`);
            return;
        }

        logger.info(
            `[${this.company}] Backing up ${fileNames.length} downloaded files`,
        );

        for (const fileName of fileNames) {
            try {
                await fileUtil.backupDownloadFiles(this.company, fileName);
                logger.info(
                    `[${this.company}] Downloaded file backed up: ${fileName}`,
                );
            } catch (error) {
                logger.error(
                    `[${this.company}] Error backing up file ${fileName}: ${error.message}`,
                );
                throw error;
            }
        }

        logger.info(`[${this.company}] Downloaded files backup completed`);
    }

    /**
     * 備份未上傳的檔案
     */
    async backupNotUploadFiles(): Promise<void> {
        try {
            logger.info(`[${this.company}] Backing up not uploaded files`);
            await fileUtil.backupNotUploadFiles(this.company);
            logger.info(
                `[${this.company}] Not uploaded files backup completed`,
            );
        } catch (error) {
            logger.error(
                `[${this.company}] Error backing up not uploaded files: ${error.message}`,
            );
            throw error;
        }
    }

    /**
     * 備份上傳檔案
     */
    async backupUploadFiles(): Promise<void> {
        try {
            logger.info(`[${this.company}] Backing up upload files`);
            await fileUtil.backupUploadFiles(this.company);
            logger.info(`[${this.company}] Upload files backup completed`);
        } catch (error) {
            logger.error(
                `[${this.company}] Error backing up upload files: ${error.message}`,
            );
            throw error;
        }
    }

    /**
     * 備份 ACH 檔案
     */
    async backupACHFiles(): Promise<void> {
        try {
            logger.info(`[${this.company}] Backing up ACH files`);
            await fileUtil.backupACHFiles(this.company);
            logger.info(`[${this.company}] ACH files backup completed`);
        } catch (error) {
            logger.error(
                `[${this.company}] Error backing up ACH files: ${error.message}`,
            );
            throw error;
        }
    }

    /**
     * 取得檔案路徑列表
     * @param folderPath - 資料夾路徑
     * @returns 檔案路徑列表
     */
    getFilePaths(folderPath: string): string[] {
        try {
            const filePaths = fileUtil.getFilePaths(folderPath);
            logger.info(
                `[${this.company}] Found ${filePaths.length} files in ${folderPath}`,
            );
            return filePaths;
        } catch (error) {
            logger.error(
                `[${this.company}] Error getting file paths from ${folderPath}: ${error.message}`,
            );
            throw error;
        }
    }

    /**
     * 驗證檔案完整性
     * @param fileNames - 檔案名稱列表
     * @returns 驗證結果
     */
    async validateFileIntegrity(fileNames: string[]): Promise<{
        valid: string[];
        invalid: string[];
    }> {
        const result = {
            valid: [] as string[],
            invalid: [] as string[],
        };

        logger.info(
            `[${this.company}] Validating ${fileNames.length} files integrity`,
        );

        for (const fileName of fileNames) {
            try {
                // 這裡可以實作檔案完整性檢查邏輯
                // 例如：檢查檔案大小、格式、內容等
                const isValid = await this.checkFileExists(fileName);

                if (isValid) {
                    result.valid.push(fileName);
                } else {
                    result.invalid.push(fileName);
                }
            } catch (error) {
                logger.error(
                    `[${this.company}] Error validating file ${fileName}: ${error.message}`,
                );
                result.invalid.push(fileName);
            }
        }

        logger.info(
            `[${this.company}] File validation completed: ${result.valid.length} valid, ${result.invalid.length} invalid`,
        );

        return result;
    }

    /**
     * 檢查檔案是否存在
     * @param filePath - 檔案路徑
     * @returns 檔案是否存在
     */
    private async checkFileExists(filePath: string): Promise<boolean> {
        try {
            // 這裡可以實作檔案存在檢查邏輯
            // 暫時返回 true，實際實作需要根據 fileUtil 的 API
            logger.info(
                `[${this.company}] Checking file existence: ${filePath}`,
            );
            return true;
        } catch (error) {
            logger.error(
                `[${this.company}] Error checking file existence ${filePath}: ${error.message}`,
            );
            return false;
        }
    }

    /**
     * 清理臨時檔案
     * @param olderThanDays - 清理多少天前的檔案
     */
    async cleanupTempFiles(olderThanDays = 7): Promise<void> {
        try {
            logger.info(
                `[${this.company}] Cleaning up temporary files older than ${olderThanDays} days`,
            );

            // 這裡可以實作清理邏輯
            // 例如：刪除超過指定天數的臨時檔案

            logger.info(`[${this.company}] Temporary files cleanup completed`);
        } catch (error) {
            logger.error(
                `[${this.company}] Error cleaning up temporary files: ${error.message}`,
            );
            throw error;
        }
    }
}
