import { OrderService } from './order.service';
import { EsunService } from './esun.service';
import logger from './logger.service';
import { RepayService } from './repay.service';
import * as moment from 'moment';
import { EnumPayStateType } from './interfaces/common.interface';
import { BankACHService } from './bankACH.service';
import { HolidayService } from './holiday.service';
import { Order } from './interfaces/order.interface';
import { TaskUseCaseFactory } from './usecases';
import { UpdatePaymentResult } from './interfaces/payment.interface';

/**
 * 主要的任務服務類別
 * 只包含主要的業務流程步驟，細節實作委託給各個 Use Cases
 */
export class TaskService {
    private company: string;
    private shipDate?: Date;
    private orderService: OrderService;
    private esunService: EsunService;
    private repayService: RepayService;
    private bankACHService: BankACHService;
    private holidayService: HolidayService;
    private useCases: TaskUseCaseFactory;

    constructor(company: string, shipDate?: Date) {
        this.company = company;
        this.shipDate = shipDate;
        this.orderService = new OrderService(this.company);
        this.esunService = new EsunService(this.company);
        this.repayService = new RepayService(this.company);
        this.bankACHService = new BankACHService(this.company);
        this.holidayService = new HolidayService();
        this.useCases = new TaskUseCaseFactory(this.company);

        // 設定事件監聽器
        this.setupEventListeners();
    }

    /**
     * 設定玉山服務的事件監聽器
     */
    private setupEventListeners(): void {
        this.esunService.emitter.on('jar_upload_closed', async (data) => {
            logger.info(`[${this.company}] ${data.stdout}`);
            await this.useCases.getManageFilesUseCase().backupNotUploadFiles();
        });

        this.esunService.emitter.on('jar_download_closed', async (data) => {
            logger.info(`[${this.company}] ${data.stdout}`);
            await this.generateResponse({ isRepay: false });
        });

        this.esunService.emitter.on(
            'jar_repay_download_closed',
            async (data) => {
                logger.info(`[${this.company}] ${data.stdout}`);
                await this.generateResponse({ isRepay: true });
            },
        );
    }

    /**
     * 主要上傳授權流程
     * @param shipDate - 出貨日期
     */
    async upload(shipDate?: Date): Promise<void> {
        logger.info(`[${this.company}] Start generate request`);
        const orders = await this.orderService.findByDateForPay(shipDate);

        // 1. 建立出貨單並記錄
        const info = await this.useCases
            .getCreateShipperUseCase()
            .execute(orders, shipDate);
        logger.info(
            `[${this.company}] ShippingOrder Created! (${info.total})
            CC: ${info.cc}
            Cash: ${info.cash}
            Free: ${info.free}
            ACH: ${info.ACH}`,
        );

        // 2. 處理信用卡訂單
        await this.processCardOrders(shipDate);

        // 3. 處理 ACH 相關流程
        await this.processACH();
    }

    /**
     * 處理信用卡訂單的授權請求
     * @param shipDate - 出貨日期
     */
    private async processCardOrders(shipDate?: Date): Promise<void> {
        const ordersByCard = await this.orderService.findByDateForPayByCard(
            shipDate,
        );
        await this.useCases
            .getProcessCardPaymentUseCase()
            .execute(ordersByCard, false, 'Card orders');
    }

    /** 提前兩天將ACH產出 */
    async exportACH(isRepay: boolean, shipDate?: Date) {
        logger.info(
            `[${this.company}] Start generate ${
                isRepay ? 'repay' : ''
            }ACH request`,
        );
        //寄送玉山日期
        const sendDate = shipDate
            ? moment(shipDate).toDate()
            : moment().toDate();
        //非營業日不產生檔案與寄送給玉山
        const isBusinessDay = await this.holidayService.checkBusinessDay(
            sendDate,
        );
        if (!isBusinessDay) {
            return;
        }

        //取得交易檔日期
        const ACHDate = await this.bankACHService.getACHDate(sendDate);

        let ordersByACH: Order[] = [];

        if (isRepay) {
            ordersByACH = await this.repayService.search({
                beforeDate: shipDate,
                state: EnumPayStateType.Unpaid,
                isACH: true,
            });
        } else {
            // achDate1 + 1D ~ lastACHDate
            ordersByACH = await this.orderService.findByDateForPayByACH({
                achDate1: moment(ACHDate.perACHDate).add(1, 'day').toDate(),
                achDate2: ACHDate.lastACHDate,
            });
        }

        if (ordersByACH.length > 0) {
            await this.bankACHService.generateRequest({
                orders: ordersByACH,
                achDate: ACHDate.lastACHDate,
                isRepay,
            });
            logger.info(
                `[${this.company}] ${
                    isRepay ? 'repay' : ''
                } ACH file created! (${ordersByACH.length})`,
            );
        }

        // 處理補扣款相關的後續作業
        if (isRepay && ordersByACH.length > 0) {
            await this.processRepayACHOrders(ordersByACH, ACHDate.lastACHDate);
        }

        return ACHDate.lastACHDate;
    }

    /**
     * 處理補扣款 ACH 訂單的後續作業
     * @param orders - 訂單列表
     * @param achDate - ACH 日期
     */
    private async processRepayACHOrders(
        orders: Order[],
        achDate: Date,
    ): Promise<void> {
        // 這裡需要 AskingService，暫時先加回來
        const { AskingService } = await import('./asking.service');
        const askingService = new AskingService(this.company);

        for (const order of orders) {
            await this.repayService.updateProcessStatus({
                filters: {
                    applicationCode: order.applicationId,
                    period: order.period,
                },
            });

            await askingService.replyProcessACH({
                applicationId: order.applicationId,
                period: order.period,
                achDate: moment(achDate).format('YYYY-MM-DD'),
            });
        }
    }

    /**
     * 處理 ACH 相關流程
     */
    async processACH(): Promise<void> {
        // 1. 首次 ACH 產出
        const ACHDate = await this.exportACH(false);
        // 2. 補扣款 ACH 產出
        const repayACHDate = await this.repayACH();

        // 3. 發送 ACH 郵件並備份檔案
        await this.useCases
            .getProcessACHUseCase()
            .sendMailAndBackupFiles(ACHDate, repayACHDate);
    }

    /**
     * 每週第一個工作天＆倒數第二個工作天執行補扣款 ACH
     * @param date - 檢查日期
     */
    async repayACH(date: Date = moment().toDate()): Promise<Date | undefined> {
        // 1. 檢查是否為每週第一個工作天或倒數第二個工作天
        const shouldExport = await this.useCases
            .getProcessACHUseCase()
            .checkBusinessDaysForRepayACH(date);
        if (shouldExport) {
            // 2. 導出補扣款 ACH
            return await this.exportACH(true, date);
        }
        return;
    }

    /**
     * 下載玉山回應檔案
     */
    async download(): Promise<void> {
        logger.info(`[${this.company}] Start generate response`);
        await this.esunService.execDownload({ isRepay: false });
        logger.info(`[${this.company}] Response downloaded!`);
    }

    /**
     * 處理補刷卡上傳流程
     */
    async repayUpload(): Promise<void> {
        const feeDate = moment().toDate();
        logger.info(`[${this.company}] Start repay generate request`);

        // 取得需要補刷的信用卡訂單
        const ordersByCard = await this.repayService.search({
            feeDate,
            state: EnumPayStateType.Unpaid,
            isCard: true,
        });

        await this.useCases
            .getProcessCardPaymentUseCase()
            .execute(ordersByCard, true, 'Repay orders');
    }

    /**
     * 下載補刷卡回應檔案
     */
    async repayDownload(): Promise<void> {
        logger.info(`[${this.company}] Start repay generate response`);
        await this.esunService.execDownload({ isRepay: true });
        logger.info(`[${this.company}] Response repay downloaded!`);
    }

    /**
     * 處理玉山銀行回應檔案的主要流程
     * @param params - 包含是否為補刷卡的參數
     */
    private async generateResponse(params: {
        isRepay: boolean;
    }): Promise<void> {
        try {
            // 1. 產生並解析玉山回應檔案
            const responseResult = await this.useCases
                .getProcessCardPaymentUseCase()
                .processResponse(this.shipDate, params.isRepay);
            if (!responseResult) {
                return;
            }

            // 2. 更新出貨單狀態
            const shippingResults = await this.useCases
                .getCreateShipperUseCase()
                .updateStatus(responseResult.data);

            // 3. 處理交易結果(交易失敗＆補刷處理)
            await this.processTransactionResults(
                shippingResults,
                params.isRepay,
            );

            // 4. 備份已下載的檔案
            await this.useCases
                .getManageFilesUseCase()
                .backupDownloadedFiles(responseResult.filesName);

            logger.info(
                `[${this.company}] Response processing completed successfully`,
            );
        } catch (error) {
            logger.error(
                `[${this.company}] Error in generateResponse: ${error.message}`,
            );
            throw error;
        }
    }

    /**
     * 處理交易結果（成功/失敗）
     * @param shippingResults - 出貨單列表
     * @param isRepay - 是否為補刷卡
     */
    private async processTransactionResults(
        shippingResults: UpdatePaymentResult[],
        isRepay: boolean,
    ): Promise<void> {
        for (const shippingResult of shippingResults) {
            if (isRepay) {
                await this.handleRepayShipper(shippingResult);
            } else if (shippingResult.respondCode !== '00') {
                await this.handleFailedShipper(shippingResult);
            }
        }
    }

    /**
     * 處理補刷卡交易結果
     * @param shippingResult - 出貨單資料
     */
    private async handleRepayShipper(
        shippingResult: UpdatePaymentResult,
    ): Promise<void> {
        try {
            const isSuccess = shippingResult.respondCode === '00';

            if (isSuccess) {
                await this.useCases
                    .getProcessRepayUseCase()
                    .processSuccess(shippingResult);
            } else {
                await this.useCases
                    .getProcessRepayUseCase()
                    .processFailure(shippingResult);
            }
        } catch (error) {
            logger.error(
                `[${this.company}] Error handling repay shippingResult ${shippingResult.shipperId}: ${error.message}`,
            );
            throw error;
        }
    }

    /**
     * 處理交易失敗的出貨單
     * @param shippingResult - 出貨單資料
     */
    private async handleFailedShipper(
        shippingResult: UpdatePaymentResult,
    ): Promise<void> {
        try {
            // 處理失敗付款的完整流程
            const askingId = await this.useCases
                .getProcessFailedPaymentUseCase()
                .execute(shippingResult);

            logger.warn(
                `[${this.company}] Payment failed for ${shippingResult.applicationId}-${shippingResult.period}: ${shippingResult.respondMessage} (#${askingId})`,
            );
        } catch (error) {
            logger.error(
                `[${this.company}] Error handling failed shippingResult ${shippingResult.shipperId}: ${error.message}`,
            );
            throw error;
        }
    }
}
