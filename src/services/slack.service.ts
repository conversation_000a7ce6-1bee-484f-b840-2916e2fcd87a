import { Utils, Helpers } from '@clinico/clinico-node-framework';
import * as moment from 'moment';
import configs from '../configs';

class SlackService {
    private clockImg: string;
    private isInitialized: boolean = false;

    constructor() {
        try {
            Utils.Logger.Slack.initialize({
                url: configs.notification.slack.webhookUrl,
                defaults: undefined,
            });
            this.isInitialized = true;
        } catch (error) {
            console.error('Failed to initialize Slack service:', error.message);
            this.isInitialized = false;
        }

        this.clockImg = 'https://img.icons8.com/B0B0B0/calendar';
    }

    /**
     * 安全地發送 Slack 訊息，避免錯誤影響主要業務流程
     * @param slackMethod - Slack 發送方法
     * @param payload - 訊息內容
     * @param messageType - 訊息類型（用於日誌）
     */
    private async sendSafely(
        slackMethod: Function,
        payload: any,
        messageType: string,
    ): Promise<void> {
        // 如果 Slack 未正確初始化，直接返回
        if (!this.isInitialized) {
            console.warn(`Slack service not initialized, skipping ${messageType} message`);
            return;
        }

        try {
            // 設定超時時間為 5 秒，避免長時間等待
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Slack request timeout')), 5000);
            });

            const slackPromise = slackMethod(payload);

            await Promise.race([slackPromise, timeoutPromise]);
        } catch (error) {
            // 記錄錯誤但不拋出，避免影響主要業務流程
            console.error(`Failed to send ${messageType} message to Slack:`, error.message);

            // 可選：記錄到檔案或其他日誌系統
            // 這裡不使用 logger.service 避免循環依賴
        }
    }

    async info(message: string): Promise<void> {
        const payload = {
            title: ':ghost: Info',
            text: message,
            footer: Helpers.Date.formatDateTime(moment()),
            footerIcon: this.clockImg,
        };

        await this.sendSafely(
            Utils.Logger.Slack.info.bind(Utils.Logger.Slack),
            payload,
            'info',
        );
    }

    async warning(message: string): Promise<void> {
        const payload = {
            title: ':face_with_monocle: WARNING!',
            text: message,
            footer: Helpers.Date.formatDateTime(moment()),
            footerIcon: this.clockImg,
        };

        await this.sendSafely(
            Utils.Logger.Slack.warning.bind(Utils.Logger.Slack),
            payload,
            'warning',
        );
    }

    async error(message: string): Promise<void> {
        const payload = {
            title: ':poop: Oh shit!',
            text: message,
            footer: Helpers.Date.formatDateTime(moment()),
            footerIcon: this.clockImg,
        };

        await this.sendSafely(
            Utils.Logger.Slack.error.bind(Utils.Logger.Slack),
            payload,
            'error',
        );
    }
}

const slack = new SlackService();
export default slack;
