import { AskingService } from '../asking.service';
import { UpdatePaymentResult } from '../interfaces/payment.interface';
import { SMSService } from '../sms.service';
import { BaseUseCase, DataValidatorHelper } from './shared';

/**
 * 處理補刷卡的業務用例
 * 負責補刷卡成功和失敗的處理流程
 *
 * 重構後使用共用工具來避免重複程式碼
 */
export class ProcessRepayUseCase extends BaseUseCase {
    private askingService: AskingService;
    private dataValidatorHelper: DataValidatorHelper;
    private smsService: SMSService;

    constructor(company: string) {
        super(company);
        this.askingService = new AskingService(this.company);
        this.dataValidatorHelper = new DataValidatorHelper(company);
        this.smsService = new SMSService(this.company);
    }

    /**
     * 處理補刷卡成功的情況
     * @param shippingResult - 出貨單資料
     */
    async processSuccess(shippingResult: UpdatePaymentResult): Promise<void> {
        return this.executeWithErrorHandling(
            async () => {
                // 驗證出貨單資料
                this.validateShipperData(shippingResult);

                this.logInfo(
                    `Processing repay success for ${shippingResult.applicationId}-${shippingResult.period}`,
                );

                await this.askingService.repayCompleted({
                    applicationId: shippingResult.applicationId,
                    period: shippingResult.period,
                });

                this.logInfo(
                    `Repay completed for ${shippingResult.applicationId}-${shippingResult.period}`,
                );
            },
            'process repay success',
            {
                applicationId: shippingResult.applicationId,
                period: shippingResult.period,
            },
        );
    }

    /**
     * 處理補刷卡失敗的情況
     * @param shippingResult - 出貨單資料
     */
    async processFailure(shippingResult: UpdatePaymentResult): Promise<void> {
        return this.executeWithErrorHandling(
            async () => {
                // 驗證出貨單資料
                this.validateShipperData(shippingResult);

                this.logInfo(
                    `Processing repay failure for ${shippingResult.applicationId}-${shippingResult.period}`,
                );

                // 1. 發送失敗通知
                await this.sendFailureNotifications(shippingResult);

                // 2. 更新asking補刷紀錄
                await this.recordAskingFailure(shippingResult);

                this.logWarn(
                    `Repay failed for ${shippingResult.applicationId}-${shippingResult.period}: ${shippingResult.respondMessage}`,
                );
            },
            'process repay failure',
            {
                applicationId: shippingResult.applicationId,
                period: shippingResult.period,
                errorMessage: shippingResult.respondMessage,
            },
        );
    }

    /**
     * 根據回應代碼處理補刷卡結果
     * @param shippingResult - 出貨單資料
     */
    async processResult(shippingResult: UpdatePaymentResult): Promise<void> {
        const isSuccess = this.dataValidatorHelper.isPaymentSuccessful(
            shippingResult.respondCode,
        );

        if (isSuccess) {
            await this.processSuccess(shippingResult);
        } else {
            await this.processFailure(shippingResult);
        }
    }

    /**
     * 驗證出貨單資料
     * @param shipper - 出貨單資料
     */
    private validateShipperData(shippingResult: UpdatePaymentResult): void {
        const requiredFields = ['applicationId', 'period', 'respondCode'];
        this.dataValidatorHelper.validateRequiredFields(
            shippingResult,
            requiredFields,
            'shippingResult',
        );
    }

    /**
     * 發送失敗通知（簡訊）
     * @param shippingResult - 出貨單資料
     */
    async sendFailureNotifications(
        shippingResult: UpdatePaymentResult,
    ): Promise<void> {
        await this.smsService.sendFailedToMember(
            {
                orderType: shippingResult.orderType,
                applicationId: shippingResult.applicationId,
                period: shippingResult.period,
                errorCode: shippingResult.respondCode,
                errorMessage: shippingResult.respondMessage,
                storeCode: shippingResult.storeId,
                amount: shippingResult.amount,
                method: shippingResult.method,
            },
            true,
            'SCHEDULE', // 標記為排程來源
        );

        this.logInfo(
            `Failure SMS sent for repay ${shippingResult.applicationId}-${shippingResult.period}`,
        );
    }

    /**
     * 記錄 Asking 系統的失敗案件
     * @param shippingResult - 出貨單資料
     * @returns Asking ID
     */
    async recordAskingFailure(
        shippingResult: UpdatePaymentResult,
    ): Promise<void> {
        await this.askingService.repayFailed({
            applicationId: shippingResult.applicationId,
            period: shippingResult.period,
            errorMessage: shippingResult.respondMessage,
        });
    }
}
