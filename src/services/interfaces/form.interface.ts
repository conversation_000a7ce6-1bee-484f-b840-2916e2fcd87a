export interface WPCApplicationForm {
    RSID: string;
    MEMBERID: string;
    MEMBERNAME: string;
    PERIOD: string;
    SOCODE: string;
    STARTDATE: Date;
    CHANGEDDATE: Date;
    ENDDATE: Date;
    CCDID: string;
    EXPIREDDATE: string;
    TAXID: string;
    FINES: number;
    LINE: number;
    ITEMCODE: string;
    ITEMNAME: string;
    ITEMSPEC: string;
    SN: string;
    CUSTOMERID: string;
    INVOICETAKE: string;
    APAPMODEL: string;
    MASKKIND: string;
    OTHER: string;
    MASKITEM: string;
    RETURNDATE: Date;
    CANCELDATE: Date;
    RSSTATUS: string;
    REPAIRSTATUS: string;
    IDENTITYCARD: string;
    EMAIL: string;
    TELEPHONE: string;
    MOBILEPHONE: string;
    POSTCODE: string;
    ADDRESS: string;
    CARDFAILED: string;
    SHIPDATE: Date;
    MEMO: string;
    RSDESC: string;
    SOURCE: string;
    REFERENCEID: string;
    CREATEBY: string;
    CREATEDATE: Date;
    MODIFIEDBY: string;
    MODIFIEDDATE: Date;
    STOREID: string;
    LEGAL: string;
    USER_EMAIL: string;
    USER_CODE: string;
}

export interface IApplicationForm {
    applicationCode: string;
    orderCode: string;
    memberCode: string;
    userCode: string;
    cardNumber?: string;
    storeCode: string;
    mobilePhone?: string;
}

export interface ISearchApplicationFormParams {
    orderId?: string;
    applicationId?: string;
}
