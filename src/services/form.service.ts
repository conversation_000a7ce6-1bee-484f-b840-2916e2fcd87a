import wpcHaDb from '../databases/tiptop.ha.database';
import wpcSkdDb from '../databases/tiptop.skd.database';
import wpcIbDb from '../databases/tiptop.ib.database';
import logger from './logger.service';
import { CLINICO, SKD, IB } from '../const';
import {
    ISearchApplicationFormParams,
    IApplicationForm,
    WPCApplicationForm,
} from './interfaces/form.interface';

export class FormService {
    private company: string;
    constructor(company: string) {
        this.company = company;
    }
    async findOne(id: string): Promise<IApplicationForm | null> {
        const form = await this.searchApplicationForm({ applicationId: id });
        if (form.length != 1) {
            return null;
        }
        return form[0];
    }

    async searchApplicationForm(
        params: ISearchApplicationFormParams,
    ): Promise<IApplicationForm[]> {
        let sql = `select form.*,
            o.OEA14 as USER_CODE
            FROM rs_application_form form
            join OEA_FILE o on o.OEA01 = form.SOCODE
            WHERE 1 = 1 `;

        if (params.orderId) {
            sql += ` AND form.SOCODE = '${params.orderId}' `;
        }
        if (params.applicationId) {
            sql += ` AND form.RSID = '${params.applicationId}' `;
        }

        try {
            let rows: WPCApplicationForm[] = [];

            switch (this.company) {
                case CLINICO:
                    rows = await wpcHaDb.raw<WPCApplicationForm[]>(sql);
                    break;
                case SKD:
                    rows = await wpcSkdDb.raw<WPCApplicationForm[]>(sql);
                    break;
                case IB:
                    rows = await wpcIbDb.raw<WPCApplicationForm[]>(sql);
                    break;
            }

            const result: IApplicationForm[] = [];
            for (const row of rows) {
                result.push({
                    applicationCode: row.RSID,
                    orderCode: row.SOCODE,
                    memberCode: row.MEMBERID,
                    userCode: row.USER_CODE,
                    cardNumber: row.CCDID,
                    storeCode: row.STOREID,
                    mobilePhone: row.MOBILEPHONE,
                });
            }
            return result;
        } catch (err) {
            logger.error(`[${this.company}] 申請書查詢失敗: ${err} `);
            return [];
        }
    }
}
