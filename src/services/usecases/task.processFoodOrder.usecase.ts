import { OrderService } from '../order.service';
import * as moment from 'moment';
import { BaseUseCase, DataValidatorHelper } from './shared';

/**
 * 處理食品訂單的業務用例
 * 負責搜尋和處理食品訂閱訂單
 *
 * 重構後使用共用工具來避免重複程式碼
 */
export class ProcessFoodOrderUseCase extends BaseUseCase {
    private orderService: OrderService;
    private dataValidatorHelper: DataValidatorHelper;

    constructor(company: string) {
        super(company);
        this.orderService = new OrderService(this.company);
        this.dataValidatorHelper = new DataValidatorHelper(company);
    }

    /**
     * 搜尋待處理的食品訂單
     * @param cardDate - 刷卡日期
     * @returns 訂單列表（包含一般訂單和補刷訂單）
     */
    async searchPendingOrders(cardDate?: Date): Promise<any[]> {
        return this.executeWithErrorHandling(
            async () => {
                this.logInfo(
                    `Searching pending food orders for ${moment(
                        cardDate,
                    ).format('YYYY-MM-DD')}`,
                );

                // 1. 找出待刷訂單（申請書出貨日前10天要觸發刷卡）
                const regularOrders = await this.searchRegularOrders(cardDate);
                this.logInfo(
                    `Found ${regularOrders.length} regular food orders`,
                );

                // 2. 找出補刷訂單
                const repayOrders = await this.searchRepayOrders(cardDate);
                this.logInfo(`Found ${repayOrders.length} repay food orders`);

                // 3. 合併訂單
                const allOrders = regularOrders.concat(repayOrders);
                this.logInfo(
                    `Total food orders to process: ${allOrders.length}`,
                );

                return allOrders;
            },
            'search pending food orders',
            { cardDate: moment(cardDate).format('YYYY-MM-DD') },
        );
    }

    /**
     * 搜尋一般待刷訂單
     * @param cardDate - 刷卡日期
     * @returns 一般訂單列表
     */
    private async searchRegularOrders(cardDate?: Date): Promise<any[]> {
        const searchParams = {
            // 申請書出貨日前10天要觸發刷卡
            nextShippingDate: moment(cardDate)
                .add(10, 'days')
                .format('YYYY-MM-DD'),
        };

        const orders = await this.orderService.searchFood(
            searchParams,
            cardDate,
        );
        this.logInfo('Regular orders search params', searchParams);

        return orders;
    }

    /**
     * 搜尋補刷訂單
     * @param cardDate - 刷卡日期
     * @returns 補刷訂單列表
     */
    private async searchRepayOrders(cardDate?: Date): Promise<any[]> {
        const searchParams = {
            estimatedCardDate: moment(cardDate).format('YYYY-MM-DD'),
            isRepay: true,
        };

        const orders = await this.orderService.searchFood(
            searchParams,
            cardDate,
        );
        this.logInfo('Repay orders search params', searchParams);

        return orders;
    }

    /**
     * 取得單一訂閱明細
     * @param orderId - 訂單ID
     * @returns 訂閱明細
     */
    async getSubscribeDetail(orderId: string): Promise<any> {
        return this.executeWithErrorHandling(
            async () => {
                // 驗證訂單ID
                if (
                    !this.dataValidatorHelper.validateStringLength(
                        orderId,
                        'orderId',
                        1,
                    )
                ) {
                    return null;
                }

                const subscribeDetail =
                    await this.orderService.getOneSubscribeDetailByOrderID(
                        orderId,
                    );

                if (!subscribeDetail) {
                    this.logWarn(
                        `Subscribe detail not found for order: ${orderId}`,
                    );
                }

                return subscribeDetail;
            },
            'get subscribe detail',
            { orderId },
        );
    }

    /**
     * 更新訂閱卡片狀態
     * @param orderId - 訂單ID
     * @param updateData - 更新資料
     */
    async updateSubscribeCardState(
        orderId: string,
        updateData: {
            bankRespondCode?: string;
            askingId?: number;
            shippingId?: string;
            isCompleted?: string;
        },
    ): Promise<void> {
        return this.executeWithErrorHandling(
            async () => {
                // 驗證訂單ID
                if (
                    !this.dataValidatorHelper.validateStringLength(
                        orderId,
                        'orderId',
                        1,
                    )
                ) {
                    return;
                }

                await this.orderService.updateSubscribeCardStateByOrderId(
                    orderId,
                    updateData,
                );

                this.logInfo('Subscribe card state updated', {
                    orderId,
                    updateData,
                });
            },
            'update subscribe card state',
            { orderId, updateData },
        );
    }

    /**
     * 驗證訂單資料
     * @param authorizeRecord - 授權記錄
     * @returns 是否有效
     */
    validateOrderData(authorizeRecord: any): boolean {
        return this.dataValidatorHelper.validateOrderData(authorizeRecord);
    }
}
