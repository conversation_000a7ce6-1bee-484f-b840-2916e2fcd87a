import logger from '../../logger.service';

/**
 * 基礎 Use Case 抽象類別
 * 提供所有 Use Cases 的共同功能，遵循 Clean Architecture 原則
 */
export abstract class BaseUseCase {
    protected readonly company: string;

    constructor(company: string) {
        this.company = company;
    }

    /**
     * 執行 Use Case 的主要方法，包含統一的錯誤處理
     * @param operation - 要執行的操作
     * @param operationName - 操作名稱（用於日誌）
     * @param params - 操作參數
     * @returns 操作結果
     */
    protected async executeWithErrorHandling<T>(
        operation: () => Promise<T>,
        operationName: string,
        params?: any,
    ): Promise<T> {
        try {
            this.logOperationStart(operationName, params);
            const result = await operation();
            this.logOperationSuccess(operationName);
            return result;
        } catch (error) {
            this.logOperationError(operationName, error);
            throw error;
        }
    }

    /**
     * 記錄操作開始
     * @param operationName - 操作名稱
     * @param params - 操作參數
     */
    protected logOperationStart(operationName: string, params?: any): void {
        const paramsStr = params
            ? ` with params: ${JSON.stringify(params)}`
            : '';
        logger.info(`[${this.company}] Starting ${operationName}${paramsStr}`);
    }

    /**
     * 記錄操作成功
     * @param operationName - 操作名稱
     */
    protected logOperationSuccess(operationName: string): void {
        logger.info(
            `[${this.company}] ${operationName} completed successfully`,
        );
    }

    /**
     * 記錄操作錯誤
     * @param operationName - 操作名稱
     * @param error - 錯誤物件
     */
    protected logOperationError(operationName: string, error: any): void {
        logger.error(
            `[${this.company}] Error in ${operationName}: ${error.message}`,
        );
    }

    /**
     * 記錄資訊日誌
     * @param message - 日誌訊息
     * @param data - 額外資料
     */
    protected logInfo(message: string, data?: any): void {
        const dataStr = data ? ` - ${JSON.stringify(data)}` : '';
        logger.info(`[${this.company}] ${message}${dataStr}`);
    }

    /**
     * 記錄警告日誌
     * @param message - 日誌訊息
     * @param data - 額外資料
     */
    protected logWarn(message: string, data?: any): void {
        const dataStr = data ? ` - ${JSON.stringify(data)}` : '';
        logger.warn(`[${this.company}] ${message}${dataStr}`);
    }

    /**
     * 記錄錯誤日誌
     * @param message - 日誌訊息
     * @param error - 錯誤物件
     */
    protected logError(message: string, error?: any): void {
        const errorStr = error ? `: ${error.message}` : '';
        logger.error(`[${this.company}] ${message}${errorStr}`);
    }

    /**
     * 驗證必要參數
     * @param params - 參數物件
     * @param requiredFields - 必要欄位列表
     * @param entityName - 實體名稱（用於錯誤訊息）
     */
    protected validateRequiredFields(
        params: any,
        requiredFields: string[],
        entityName = 'entity',
    ): void {
        for (const field of requiredFields) {
            if (!params[field]) {
                throw new Error(
                    `Missing required field for ${entityName}: ${field}`,
                );
            }
        }
    }

    /**
     * 檢查陣列是否為空
     * @param array - 要檢查的陣列
     * @param arrayName - 陣列名稱（用於日誌）
     * @returns 是否為空
     */
    protected checkEmptyArray<T>(array: T[], arrayName: string): boolean {
        if (array.length === 0) {
            this.logInfo(`No ${arrayName} to process`);
            return true;
        }
        return false;
    }

    /**
     * 安全執行操作，捕獲錯誤但不拋出
     * @param operation - 要執行的操作
     * @param operationName - 操作名稱
     * @returns 操作結果或 null（如果發生錯誤）
     */
    protected async safeExecute<T>(
        operation: () => Promise<T>,
        operationName: string,
    ): Promise<T | null> {
        try {
            return await operation();
        } catch (error) {
            this.logError(`Safe execution failed for ${operationName}`, error);
            return null;
        }
    }
}
