class StringUtil {
    paddingLeftForInteger(num: number, length: number, char: string): string {
        if (char.length != 1) {
            throw new Error('Invalid padding char');
        }
        return (Array(length).join(char) + num).slice(-length);
    }

    paddingLeftForString(str: string, length: number, char: string): string {
        if (char.length != 1) {
            throw new Error('Invalid padding char');
        }
        return (Array(length).join(char) + str).slice(-length);
    }

    paddingRightForString(str: string, length: number, char: string): string {
        if (char.length != 1) {
            throw new Error('Invalid padding char');
        }
        return str.padEnd(length, char);
    }
}

const stringUtil = new StringUtil();
export default stringUtil;
