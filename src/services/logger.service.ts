import * as winston from 'winston';
import 'winston-daily-rotate-file';
import configs from '../configs';
import { Helpers } from '@clinico/clinico-node-framework';
import slack from './slack.service';

const winstonLogger = winston.createLogger({
    transports: [
        new winston.transports.Console(),
        new winston.transports.DailyRotateFile({
            dirname: configs.log.path,
            filename: `vpos-%DATE%.log`,
            zippedArchive: true,
            maxFiles: process.env.LOG_KEEP_DAYS || '120d', // 保留幾天
        }),
    ],
    format: winston.format.combine(winston.format.simple()),
});

class Logger {
    bindMessage(message: string): string {
        const datetime = Helpers.Date.formatDateTime(Helpers.Date.now());
        return `${datetime} msg: ${message}`;
    }

    info(message: string): void {
        winstonLogger.log('info', this.bindMessage(message));
        if (process.env.NODE_ENV === 'production') {
            slack.info(message);
        }
    }

    warn(message: string): void {
        winstonLogger.log('warn', this.bindMessage(message));
        if (process.env.NODE_ENV === 'production') {
            slack.warning(message);
        }
    }

    error(message: string): void {
        winstonLogger.log('error', this.bindMessage(message));
        if (process.env.NODE_ENV === 'production') {
            slack.error(message);
        }
    }

    debug(message: string, data?: any): void {
        console.log(this.bindMessage(message), data);
    }
}

const logger = new Logger();
export default logger;
