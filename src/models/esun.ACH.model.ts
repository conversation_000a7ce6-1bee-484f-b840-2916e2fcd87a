export interface EsunACHPHeaderRecord {
    /** 首錄別
     * @Length 3
     * @index 1, 3
     * @value BOF */
    P_BOF: string;
    /** 資料代號
     * @Length 6
     * @index 4, 9
     * @value ACHP01 */
    P_CDATA: string;
    /** 處理日期
     * @Length 8
     * @index 10, 17
     * @description `民國` `YYYYMMDD` (為銀行入扣帳日前一營業日) */
    P_TDATE: number;
    /** 處理時間
     * @Length 6
     * @index 18, 23
     * @formate HHMMSS */
    P_TTIME: number;
    /** 發送單位代號
     * @Length 7
     * @index 24, 30
     * @description 代表行代號 */
    P_SORG: number;
    /** 接收單位代號
     * @Length 7
     * @index 31, 37
     * @description 9990250 */
    P_RORG: number;
    /** 版次
     * @Length 3
     * @index 38, 40
     * @value V10 */
    P_VERNO: string;
    /** 備用
     * @Length 210
     * @index 41, 250 */
    FILLER: string;
}

export interface ACHPRecord {
    /** 交易型態
     * @length 1
     * @index 1, 1 */
    P_TYPE: string;
    /** 交易類別
     * @length 2
     * @index 2, 3 */
    P_TXTYPE: string;
    /** 交易代號
     * @length 3
     * @index 4, 6 */
    P_TXID: string;
    /** 交易序號
     * @length 8
     * @index 7, 14  */
    P_SEQ: string;
    /** 提出行代號(科林)
     * @length 7
     * @index 15, 21 */
    P_PBANK: string;
    /** 發動者帳號(科林)
     * @length 16
     * @index 22, 37 */
    P_PCLNO: string;
    /** 收受行代號 (提回行代號)(用戶)
     * @length 7
     * @index 38, 44 */
    P_RBANK: string;
    /** 收受者帳號(用戶)
     * @length 16
     * @index 45, 60 */
    P_RCLNO: string;
    /** 金額
     * @length 10
     * @index 61, 70 */
    P_AMT: string;
    /** 退件理由代號
     * @length 2
     * @index 71, 72 */
    P_RCODE?: string;
    /** 提示交換次序
     * @length 1
     * @index 73, 73 */
    P_SCHD: string;
    /** 發動者統一編號(科林)
     * @length 10
     * @index 74, 83 */
    P_CID: string;
    /** 收受者統一編號(用戶)
     * @length 10
     * @index 84, 93 */
    P_PID: string;
    /** 上市上櫃公司代號
     * @length 6
     * @index 94, 99 */
    P_SID: string;
    /** 原提示交易日期
     * @length 8
     * @index 100, 107 */
    P_PDATE?: string;
    /** 原提示交易序號
     * @length 8
     * @index 108, 115 */
    P_PSEQ?: string;
    /** 原提示交換次序
     * @length 1
     * @index 116, 116 */
    P_PSCHD?: string;
    /** 用戶號碼(備註用，用來跟系統對應的資訊)
     * @length 20
     * @index 117, 136 */
    P_CNO: string;
    /** 發動者專用區
     * @length 40
     * @index 137, 176 */
    P_NOTE?: string;
    /** 存摺摘要
     * @length 10
     * @index 177, 186 */
    P_MEMO?: string;
    /** 客戶支付手續費
     * @length 5
     * @index 187, 191 */
    P_CFEE?: string;
    /** 發動行專用區
     * @length 20
     * @index 192, 211 */
    P_NOTEB?: string;
    /** 備用
     * @length 39
     * @index 212, 250 */
    FILLER?: string;
}

export interface EsunACHPTrailerRecord {
    /** 尾錄別
     * @length 3
     * @index 1, 3
     * @value EOF */
    P_EOF: string;
    /** 資料代號
     * @length 6
     * @index 4, 9
     * @value ACHP01 */
    P_CDATA: string;
    /** 處理日期
     * @length 8
     * @index 10, 17
     * @description `民國` `YYYYMMDD` (為銀行入扣帳日前一營業日) */
    P_TDATE: number;
    /** 發送單位代號
     * @length 7
     * @index 18, 24
     * @description 代表行代號 */
    P_SORG: number;
    /** 接收單位代號
     * @length 7
     * @index 25, 31
     * @description '9990250' */
    P_RORG: number;
    /** 總筆數
     * @length 8
     * @index 32, 39 */
    P_TCOUNT: number;
    /** 總金額
     * @length 16
     * @index 40, 55 */
    P_TAMT: number;
    /** 前一營業日日期
     * @length 8
     * @index 56, 63
     * @description 資料代號為'ACHP01', 本欄空白 */
    P_YDATE: number;
    /** 備用
     * @length 187
     * @index 64, 250 */
    FILLER: string;
}

export interface EsunACHCPHeaderRecord {
    /** 首錄別
     * @length 3
     * @index 1, 3
     * @value BOF */
    CP_BOF: string;
    /** 資料代號
     * @length 6
     * @index 4, 9
     * @value ACHP02 */
    CP_CDATA: string;
    /** 交易日期
     * @length 8
     * @index 10, 17
     * @description `民國` `YYYYMMDD` */
    CP_TDATE: number;
    /** 發送單位代號
     * @length 7
     * @index 18, 24
     * @description 代表行代號 */
    CP_SORG: number;
    /** 版次
     * @length 3
     * @index 25, 27
     * @value V10 */
    CP_VERNO: string;
    /** 備用
     * @length 193
     * @index 28, 220 */
    FILLER: string;
}

export interface EsunACHCPRecord {
    /** 交易序號
     * @length 6
     * @index 1, 6 */
    CP_SEQ: number;
    /** 交易代號
     * @length 3
     * @index 7, 9
     * @description 909 */
    CP_TIX: string;
    /** 發動者統一編號
     * @length 10
     * @index 10, 19 */
    CP_CID: string;
    /** 提回行代號
     * @length 7
     * @index 20, 26 */
    CP_RBANK: number;
    /** 委繳戶帳號
     * @length 16
     * @index 27, 42 */
    CP_RCLNO: string;
    /** 委繳戶統一編號
     * @length 10
     * @index 43, 52 */
    CP_RID: string;
    /** 用戶號碼
     * @length 20
     * @index 53, 72
     * @description 客戶編號 */
    CP_USERNO: string;
    /** 新增或取消
     * @length 1
     * @index 73, 73 */
    CP_ADMARK: string;
    /** 資料製作日期
     * @length 8
     * @index 74, 81 */
    CP_DATE: number;
    /** 提出行代號
     * @length 7
     * @index 82, 88 */
    CP_PBANK: number;
    /** 發動者專用區
     * @length 40
     * @index 89, 128 */
    CP_NOTE: string;
    /** 交易型態
     * @length 1
     * @index 129, 129 */
    CP_TYPE: string;
    /** 回覆訊息
     * @length 1
     * @index 130, 130 */
    CP_RCODE: string;
    /** 每筆扣款限額
     * @length 8
     * @index 131, 138 */
    CP_LIMITAMT: string;
    /** 委繳帳戶性質
     * @length 1
     * @index 139, 139 */
    CP_CNTYPE: string;
    /** 授權扣款終止日
     * @length 8
     * @index 140, 147 */
    CP_EDATE: string;
    /** 發動行專用區
     * @length 20
     * @index 148, 167 */
    CP_NOTEB: string;
    /** 備用
     * @length 53
     * @index 168, 220 */
    FILLER: string;
}

export interface EsunACHCPTrailerRecord {
    /** 尾錄別
     * @length 3
     * @index 1, 3
     * @value EOF */
    CP_EOF: string;
    /** 總筆數
     * @length 8
     * @index 4, 11 */
    CP_TCOUNT: number;
    /** 備用
     * @length 209
     * @index 12, 220 */
    FILLER: string;
}
