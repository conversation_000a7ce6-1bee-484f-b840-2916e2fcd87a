import * as soap from 'soap';
import * as xml2js from 'xml2js';
import * as ip from 'ip';
import { CallParams, IAccessParams } from './interfaces/soap.interface';

export class SOAPService {
    private createClient(): Promise<soap.Client> {
        const url = process.env.SOAP_URL;

        return new Promise((resolve, reject) => {
            const options: soap.IOptions = {
                escapeXML: true,
            };
            if (!url) {
                throw new Error('soap url is empty');
            }
            soap.createClient(url, options, (err, client) => {
                if (err) reject(err);
                resolve(client);
            });
        });
    }

    async sendRequest(
        method: any,
        accessParams: IAccessParams,
        body: object,
    ): Promise<{ shipCode: string; errorMessage: string }> {
        const user = accessParams.user ? accessParams.user : 'tiptop';
        const password = accessParams.password
            ? accessParams.password
            : 'tiptop';
        const application = accessParams.application
            ? accessParams.application
            : '';

        console.log(`method name: ${method}`);

        try {
            let errorMessage = '';
            const client = await this.createClient();
            const requestData = {
                // request structure
                Request: {
                    Access: {
                        Authentication: {
                            $: { user: user, password: password },
                        },
                        Connection: {
                            $: {
                                application: application,
                                source: ip.address(),
                            },
                        },
                        Organization: { $: { name: accessParams.name } },
                        Locale: { $: { language: 'zh_tw' } },
                    },
                    RequestContent: {
                        Document: body,
                    },
                },
            };
            const xmlBuilder = new xml2js.Builder({
                headless: true,
                cdata: true,
            });
            const xmlData = xmlBuilder.buildObject(requestData);
            const soapResult = await client[method]({
                request: xmlData,
            });
            const result = await xml2js.parseStringPromise(
                soapResult[0].response,
                {
                    explicitArray: false,
                    normalizeTags: true,
                },
            );
            if (result.response.execution.status.$.code !== '0') {
                errorMessage = result.response.execution.status.$.description;
            }
            const shipCode =
                result.response.responsecontent.parameter.record.field.$.value;
            return { shipCode, errorMessage };
        } catch (err) {
            throw new Error(`SOAP error: ${err}`);
        }
    }

    async createShipOrder(
        params: CallParams,
    ): Promise<{ shipCode: string; errorMessage: string }> {
        try {
            let errorMessage = '';
            const client = await this.createClient();
            const requestData = {
                // request structure
                Request: {
                    Access: {
                        Authentication: {
                            $: { user: 'tiptop', password: '' },
                        },
                        Connection: {
                            $: {
                                application: 'resmed-vpos',
                                source: ip.address(),
                            },
                        },
                        Organization: { $: { name: params.organization } },
                        Locale: { $: { language: 'zh_tw' } },
                    },
                    RequestContent: {
                        Document: params.data,
                    },
                },
            };
            const xmlBuilder = new xml2js.Builder({
                headless: true,
                cdata: true,
            });
            const xmlData = xmlBuilder.buildObject(requestData);
            const soapResult = await client.CreateShipOrdersAsync({
                request: xmlData,
            });
            const result = await xml2js.parseStringPromise(
                soapResult[0].response,
                {
                    explicitArray: false,
                    normalizeTags: true,
                },
            );
            if (result.response.execution.status.$.code !== '0') {
                errorMessage = result.response.execution.status.$.description;
            }
            const shipCode =
                result.response.responsecontent.parameter.record.field[0].$
                    .value;
            return { shipCode, errorMessage };
        } catch (err) {
            throw new Error(`SOAP error: ${err}`);
        }
    }

    async createInvoice(
        requestData: object,
    ): Promise<{ invoiceCode: string; errorMessage: string }> {
        try {
            let errorMessage = '';
            const client = await this.createClient();
            const xmlBuilder = new xml2js.Builder({
                headless: true,
                cdata: true,
            });
            const xmlData = xmlBuilder.buildObject(requestData);
            const soapResult = await client.IssueInvoceProcessAsync({
                request: xmlData,
            });
            const result = await xml2js.parseStringPromise(
                soapResult[0].response,
                {
                    explicitArray: false,
                    normalizeTags: true,
                },
            );
            if (result.response.execution.status.$.code !== '0') {
                errorMessage = result.response.execution.status.$.description;
            }
            const invoiceCode =
                result.response.responsecontent.parameter.record.field[2].$
                    .value;
            return { invoiceCode, errorMessage };
        } catch (err) {
            throw new Error(`SOAP error: ${err}`);
        }
    }
}
