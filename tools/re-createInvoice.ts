import * as dotenvFlow from 'dotenv-flow';
const dotenv = dotenvFlow.config();
process.env.NODE_ENV = dotenv.parsed!.NODE_ENV;

import * as ip from 'ip';
import { SOAPService } from '../src/services/soap.service';

async function run() {
    try {
        const soapService = new SOAPService();
        const data = generateData('SKD-N047', 'HA501-SKD2205160001');
        const res = await soapService.createInvoice(data);
        console.log({res});
    } catch (err) {
        console.log(err);
    }
}
run();

function generateData(storeCode: string, shipCode: string): object {
    return {
        // request structure
        Request: {
            Access: {
                Authentication: {
                    $: { user: 'tiptop', password: '' },
                },
                Connection: {
                    $: {
                        application: 'resmed-vpos',
                        source: ip.address(),
                    },
                },
                Organization: { $: { name: storeCode } },
                Locale: { $: { language: 'zh_tw' } },
            },
            RequestContent: {
                Document: {
                    RecordSet: [
                        {
                            $: { id: '1' },
                            Master: {
                                $: { name: 'oga_file' },
                                Record: {
                                    Field: [
                                        {
                                            $: {
                                                name: 'oga01',
                                                value: shipCode,
                                            },
                                        },
                                    ],
                                },
                            },
                        },
                    ],
                },
            },
        },
    };
}
