stages:
  - docker
  - deploy

docker:prod:
  image: docker:18.09
  stage: docker
  only:
    - tags
  script:
    - docker build --no-cache . -t "***********:5000/resmed-vpos:${CI_COMMIT_TAG}"
    - docker push "***********:5000/resmed-vpos:${CI_COMMIT_TAG}"

deploy:prod:
  image: kroniak/ssh-client:latest
  stage: deploy
  only:
    - tags
  before_script:
    - mkdir -p ~/.ssh 
    - chmod 700 ~/.ssh
    - echo -e "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    - chmod 600 ~/.ssh/config
  script:
    - ssh clinico@$SERVER_IP 'bash -s' < .gitlab-deploy.sh ${CI_COMMIT_TAG}
  after_script:
    - rm ~/.ssh/id_rsa